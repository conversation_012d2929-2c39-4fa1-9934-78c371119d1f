
import type { GameActionData, BaseActionResult } from './game';

/**
 * Represents a single grid block in the game
 */
export interface GridBlock {
  id: string;
  row: number;
  col: number;
  isActive: boolean;
  index: number;
}

/**
 * Game data structure for Finger Frenzy
 */
export interface FingerFrenzyGameData {
  grid: GridBlock[];
  activeBlockCount: number;
}

/**
 * Result of a tile tap action
 */
export interface TileTapResult extends BaseActionResult {
  newBlock?: GridBlock | null;
  message?: string;
}

/**
 * Grid state for client synchronization
 */
export interface GridState {
  blocks: GridBlock[];
  activeCount: number;
}

/**
 * Tile states for client updates
 */
export interface TileStates {
  [tileId: string]: {
    isActive: boolean;
  };
}



export interface TileTapActionData extends GameActionData {
  action: {
    type: 'tile_tap';
    data: {
      tileId: string;
      reactionTime: number;
      clickTime?: number;
    };
  };
}
