export type BingoColumn = 'B' | 'I' | 'N' | 'G' | 'O';

export interface BingoColumnRanges {
  B: { min: 1; max: 15 };
  I: { min: 16; max: 30 };
  N: { min: 31; max: 45 };
  G: { min: 46; max: 60 };
  O: { min: 61; max: 75 };
}

export interface BingoCellData {
  id: string;           // Simple ID like "B1", "I2", "NFREE", etc.
  column: BingoColumn;
  number: number;
  position: number;     // 0-24 for 5x5 grid (0=top-left, 24=bottom-right)
  isFree: boolean;
  isMarked: boolean;
}

/**
 * Tracks numbers that have been called during the game
 */
export interface CalledNumber {
  column: BingoColumn;
  number: number;
  callOrder: number;
  timestamp: number;
}

/**
 * Winning pattern types supported by the game
 */
export type WinningPattern = 'horizontal' | 'vertical' | 'diagonal'| 'fullCard';

/**
 * Winning pattern result
 * Contains information about a completed bingo pattern
 */
export interface WinResult {
  hasWon: boolean;
  pattern?: WinningPattern;
  winningCells?: BingoCellData[];
}

/**
 * Game state information for debugging and persistence
 */
export interface GameStateInfo {
  seed: number;
  calledNumbers: number;
  availableNumbers: number;
  score: number;
  gameEnd: boolean;
  completedPatterns?: string[];
}

export interface GameEndData {
  score: number;
  winPattern?: WinningPattern | 'timeout';
}

/**
 * Pattern scoring configuration
 */
export interface PatternScoring {
  horizontal: number;
  vertical: number;
  diagonal: number;
  fullCard: number;
}

