import type { PatternScoring, WinningPattern, BingoColumnRanges } from "../types/BingoTypes";

export const DEFAULT_PATTERN_SCORES: PatternScoring = {
  horizontal: 500,
  vertical: 500,
  diagonal: 500,
  fullCard: 1000
};

export const BINGO_CONSTANTS = {
  GAME_DURATION: 60, // in seconds
  GRID_SIZE: 5,
  TOTAL_NUMBERS: 75,
  INITIAL_CALLOUT_DELAY: 500, //1500
  CALLOUT_DELAY : 1800, //2400
  FREE_SPACE_POSITION: { row: 2, col: 2 },
  COLUMNS: ['B', 'I', 'N', 'G', 'O'] as const,
  COLUMN_RANGES: {
    B: { min: 1, max: 15 },
    I: { min: 16, max: 30 },
    N: { min: 31, max: 45 },
    G: { min: 46, max: 60 },
    O: { min: 61, max: 75 }
  } as BingoColumnRanges
} as const;

/**
 * Pattern display names for UI
 */
export const PATTERN_DISPLAY_NAMES: Record<WinningPattern, string> = {
  horizontal: 'LINE BINGO!',
  vertical: 'LINE BINGO!',
  diagonal: 'DIAGONAL BINGO!',
  fullCard: 'FULL CARD!'
} as const;
