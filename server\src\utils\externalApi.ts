import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from './logger';

/**
 * Configuration for external API calls
 */
interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  apiKey?: string;
}

/**
 * Generic score submission data structure
 */
export interface ScoreSubmissionData {
  roomId: string;
  score: number;
  scoreArray: number[];
  submitScoreId?: string;
  authToken?: string;
}

/**
 * API response structure
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * External API utility class for making HTTP requests
 */
export class ExternalApiClient {
  private client: AxiosInstance;
  private config: ApiConfig;

  constructor(config: Partial<ApiConfig> = {}) {
    // Default configuration
    this.config = {
      baseUrl: process.env.EXTERNAL_API_BASE_URL || 'https://api.tictaps.com',
      timeout: parseInt(process.env.EXTERNAL_API_TIMEOUT || '10000'),
      retries: parseInt(process.env.EXTERNAL_API_RETRIES || '3'),
      apiKey: process.env.EXTERNAL_API_KEY,
      ...config
    };

    // Create axios instance
    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`External API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          headers: config.headers
        });
        return config;
      },
      (error) => {
        logger.error('External API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`External API Response: ${response.status} ${response.config.url}`, {
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('External API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Submit game score to external API
   */
  async submitScore(scoreData: ScoreSubmissionData): Promise<ApiResponse> {
    try {
      logger.info(`Submitting score for room ${scoreData.roomId}, score ${scoreData.score}`);

      // Prepare headers with auth token if provided
      const headers: any = {};
      if (scoreData.authToken) {
        headers['Authorization'] = `Bearer ${scoreData.authToken}`;
      }

      const response: AxiosResponse = await this.retryRequest(() =>
        this.client.post('/api/v1/scores/submit', scoreData, { headers })
      );

      const result: ApiResponse = {
        success: true,
        data: response.data,
        message: 'Score submitted successfully'
      };

      logger.info(`Score submission successful for room ${scoreData.roomId}`, result);
      return result;

    } catch (error: any) {
      const errorResult: ApiResponse = {
        success: false,
        error: error.message || 'Failed to submit score',
        message: `Score submission failed for room ${scoreData.roomId}`
      };

      logger.error(`Score submission failed for room ${scoreData.roomId}:`, error);
      return errorResult;
    }
  }

  /**
   * Generic GET request with retry logic
   */
  async get<T = any>(endpoint: string, params?: any): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.retryRequest(() =>
        this.client.get(endpoint, { params })
      );

      return {
        success: true,
        data: response.data,
        message: 'Request successful'
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Request failed',
        message: `GET ${endpoint} failed`
      };
    }
  }

  /**
   * Generic POST request with retry logic
   */
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.retryRequest(() =>
        this.client.post(endpoint, data)
      );

      return {
        success: true,
        data: response.data,
        message: 'Request successful'
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Request failed',
        message: `POST ${endpoint} failed`
      };
    }
  }

  /**
   * Retry logic for API requests
   */
  private async retryRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= this.config.retries; attempt++) {
      try {
        return await requestFn();
      } catch (error: any) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          throw error;
        }
        
        if (attempt < this.config.retries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          logger.warn(`API request failed (attempt ${attempt}/${this.config.retries}), retrying in ${delay}ms:`, error.message);
          await this.sleep(delay);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton instance
export const externalApiClient = new ExternalApiClient();

// Export utility functions
export const submitGameScore = (scoreData: ScoreSubmissionData) => 
  externalApiClient.submitScore(scoreData);