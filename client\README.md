# TicTaps Games - Client Application

The client-side GameApp for TicTaps Games, built with SvelteKit. This application runs inside an iframe within the main PWA.

## Overview

This SvelteKit application serves as the game client that:

- Runs embedded in an iframe within the TicTaps PWA
- Communicates with the parent PWA via postMessage API
- Connects to the Node.js game server via Socket.IO for real-time multiplayer
- Integrates games with generic UI components

## Architecture

```
PWA (Parent) --> iframe postMessage --> GameApp (This Client)
GameApp --> Socket.IO --> Node.js Game Server
Game Server --> Auth/Score APIs --> Python Backend
```

### Data Flow from PWA

The client receives the following data via postMessage:

- **Auth Token**: JWT for user authentication
- **Submit Score ID**: Unique identifier for score submission tracking
- **Room ID**: Multiplayer room identifier (if joining existing room)
- **Opponent Score**: Current opponent's score (if applicable)
- **Game ID**: Identifier for routing to specific game

### Generic UI Components

- **Countdown Timer**: Shared across all games
- **Universal HUD**: Score, time, lives, opponent score (optional)
- **End Game Screen**: Results and score submission
- **Settings**: In-game pause menu with instructions (HTP) and audio controls
- **Game Overlay System**: UI integration layer

## Project Structure

```
src/
├── lib/
│   ├── components/          # Shared UI components
│   │   ├── GameHUD.svelte
│   │   ├── Countdown.svelte
│   │   └── EndGame.svelte
│   ├── games/              # Phaser 3 game integrations
│   │   ├── FingerFrenzy/
│   │   ├── Bingo/
│   │   ├── MatchingMayhem/
│   │   └── NumberSequence/
│   ├── stores/             # Game state management
│   ├── utils/              # Utility functions
│   └── socket/             # Socket.IO client setup
├── routes/
│   ├── game/[id]/          # Dynamic game routing
│   └── +layout.svelte      # App layout
└── app.html                # Main HTML template
```

## Development

Install dependencies:

```bash
pnpm install
```

Start development server:

```bash
pnpm run dev
```

Build for production:

```bash
pnpm run build
```

See the main project `task.md` for detailed development tasks and implementation roadmap.
