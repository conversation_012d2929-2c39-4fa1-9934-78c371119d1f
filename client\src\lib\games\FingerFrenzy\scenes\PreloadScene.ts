import * as Phaser from 'phaser';
import GameConfig from '../config/GameConfig';
import { gameActions } from '$lib/stores';

export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super({ key: 'PreloadScene' });
  }

  preload(): void {
    // const { width, height } = this.cameras.main;

    // // Create loading bar
    // const bgBar = this.add.rectangle(
    //     width / 2,
    //     height / 2,
    //     width / 2,
    //     20,
    //     0x232323
    // );

    // const progressBar = this.add.rectangle(
    //     bgBar.x - bgBar.width / 2,
    //     bgBar.y,
    //     0,
    //     bgBar.height,
    //     0x00ff00
    // );
    // progressBar.setOrigin(0, 0.5);

    // const loadingText = this.add.text(
    //     width / 2,
    //     height / 2 - 30,
    //     'Loading...',
    //     {
    //       font: '24px Arial',
    //       color: '#ffffff'
    //     }
    // ).setOrigin(0.5);

    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      // progressBar.width = bgBar.width * value;
      gameActions.updateLoadingProgress(value);
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      // progressBar.destroy();
      // bgBar.destroy();
      // loadingText.destroy();
      gameActions.preloadComplete();
    });
    // Load images manually line by line
    this.load.image('block_active', '/assets-finger-frenzy/images/block_active.png');
    this.load.image('block_inactive', '/assets-finger-frenzy/images/block_inactive.png');
    this.load.image('game_name', '/assets-finger-frenzy/images/game_name.png');
    
    this.load.image('game_start', '/assets/images/game_start.png');
    this.load.image('timer_icon', '/assets/images/timer_icon.png');
    this.load.image('countdown-3', '/assets/images/countdown-3.png');
    this.load.image('countdown-2', '/assets/images/countdown-2.png');
    this.load.image('countdown-1', '/assets/images/countdown-1.png');
    this.load.image('countdown-go', '/assets/images/countdown-go.png');
    this.load.image('back_to_lobby', '/assets/images/back_to_lobby.png');
    this.load.image('game_bg', '/assets/images/game_bg.png');
    
    // Load SVG assets
    this.load.svg('heart', '/assets/images/mdi--heart.svg');
    this.load.svg('heart_outline', '/assets/images/mdi-light--heart.svg');
    this.load.svg('heart_broken', '/assets/images/mdi--heart-broken.svg');

    this.load.svg('button_bg', '/assets/images/button_bg.svg');
    this.load.svg('game_over', '/assets/images/game_over.svg');
    this.load.svg('timer_bg', '/assets/images/timer_bg.svg');
    this.load.image('timer_countdown_bg', '/assets/images/timer_countdown_bg.png');

    // Load sounds - common sounds from centralized location
    this.load.audio('click', ['/assets/audio/click.ogg', '/assets/audio/click.mp3', '/assets/audio/click.wav']);
    this.load.audio('wrong', ['/assets/audio/wrong.ogg', '/assets/audio/wrong.mp3', '/assets/audio/wrong.wav']);
    this.load.audio('countdown', ['/assets/audio/countdown.ogg', '/assets/audio/countdown.mp3', '/assets/audio/countdown.wav']);
    this.load.audio('go', ['/assets/audio/go.mp3', '/assets/audio/go.wav']);

    // Load game-specific sounds
    this.load.audio('tap', ['/assets-finger-frenzy/sounds/tap.ogg', '/assets-finger-frenzy/sounds/tap.mp3', '/assets-finger-frenzy/sounds/tap.wav']);
    this.load.audio('right', ['/assets-finger-frenzy/sounds/right.ogg', '/assets-finger-frenzy/sounds/right.mp3', '/assets-finger-frenzy/sounds/right.wav']);
    this.load.audio('timeout', ['/assets-finger-frenzy/sounds/timeout.ogg', '/assets-finger-frenzy/sounds/timeout.mp3', '/assets-finger-frenzy/sounds/timeout.wav']);
  }

  create(): void {
    // Go to start scene when loading complete
    // this.scene.start('GameStartScene');

    // gameActions.preloadComplete();
  }
}
