<script lang="ts">
  interface Props {
    handleStartClick: any;
    gameId: string;
  }
  let { handleStartClick, gameId }: Props = $props();

  // export let handleStartClick: any;
  // export const gameId = "";

  let isStarting = $state(false);
  let gameTitle: HTMLElement;
</script>

<div class="game-start-container">
  <!-- Background -->
  <div class="background"></div>

  <!-- Game Title -->
  <img
    bind:this={gameTitle}
    class="game-title pulse"
    src="/assets-{gameId}/images/game_name.png"
    alt="Game Title"
  />

  <!-- Start Button Container -->

  <button
    class="start-button"
    class:clicked={isStarting}
    onclick={handleStartClick}
    disabled={isStarting}
  >
    <div class="btn-border"></div>
    <div class="btn-background"></div>
    <span class="btn-text">START GAME</span>
  </button>
</div>

<style>
  .game-start-container {
    position: absolute;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    z-index: 100;
  }

  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("/assets/images/game_bg.png") no-repeat center center;
    background-size: cover;
    z-index: 0;
  }

  .game-title {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 70vw;
    max-height: 20vh;
    width: auto;
    height: auto;
    object-fit: contain;
    z-index: 1;
  }

  .game-title.pulse {
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      transform: translate(-50%, -50%) scale(1.02);
    }
  }

  .start-button {
    position: absolute;
    top: 60%;
    /* left: 50%; */
    /* transform: translate(-50%, -50%); */

    width: 70%;
    max-width: 350px;
    height: 100px;

    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.1s ease;
    flex-shrink: 0;
  }
  .start-button:hover {
    transform: scale(1.05);
  }

  .start-button:focus {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .start-button.clicked {
    transform: scale(0.95);
  }

  .start-button:disabled {
    cursor: not-allowed;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .game-title {
      max-width: 80vw;
      max-height: 15vh;
    }

    .start-button {
      max-width: 60vw;
      max-height: 10vh;
    }
  }

  @media (max-height: 600px) {
    .game-title {
      top: 20%;
      max-height: 15vh;
    }

    .start-button {
      top: 50%;
    }
  }
</style>
