import { Socket } from 'socket.io';
import { ExtendedError } from 'socket.io/dist/namespace';
import { jwtService } from '../services/jwtService.js';
import { AuthenticatedUser } from '../types/game.js';
import { logger } from '../utils/logger.js';

/**
 * Extended Socket interface with authenticated user data
 */
export interface AuthenticatedSocket extends Socket {
  user: AuthenticatedUser;
}

/**
 * Socket.IO authentication middleware
 * Validates JWT tokens and attaches user data to socket
 */
export function authMiddleware(socket: Socket, next: (err?: ExtendedError) => void) {
  try {
    // Extract token from auth object or handshake
    const token = socket.handshake.auth?.token || 
                  socket.handshake.headers?.authorization ||
                  socket.handshake.query?.token as string;

    if (!token) {
      logger.warn('Socket connection attempted without authentication token', {
        socketId: socket.id,
        remoteAddress: socket.handshake.address
      });
      return next(new Error('Authentication token required'));
    }

    // Validate the JWT token
    const user = jwtService.validateToken(token);
    if (!user) {
      logger.warn('Socket connection attempted with invalid token', {
        socketId: socket.id,
        remoteAddress: socket.handshake.address,
        tokenPrefix: token.substring(0, 20) + '...'
      });
      return next(new Error('Invalid authentication token'));
    }

    // Check if token is expired
    if (jwtService.isTokenExpired(token)) {
      logger.warn('Socket connection attempted with expired token', {
        socketId: socket.id,
        userId: user.userId,
        exp: user.exp
      });
      return next(new Error('Authentication token expired'));
    }

    // Attach user data to socket
    (socket as AuthenticatedSocket).user = user;

    logger.info('Socket authenticated successfully', {
      socketId: socket.id,
      userId: user.userId,
      gameId: user.gameId,
      roomId: user.roomId,
      remoteAddress: socket.handshake.address
    });

    next();
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    next(new Error('Authentication failed'));
  }
}

/**
 * Helper function to get authenticated user from socket
 * @param socket - Socket instance
 * @returns Authenticated user data or null
 */
export function getAuthenticatedUser(socket: Socket): AuthenticatedUser | null {
  return (socket as AuthenticatedSocket).user || null;
}

/**
 * Helper function to check if socket is authenticated
 * @param socket - Socket instance
 * @returns true if socket has valid user data
 */
export function isSocketAuthenticated(socket: Socket): socket is AuthenticatedSocket {
  return !!(socket as AuthenticatedSocket).user;
}

/**
 * Middleware to require authentication for specific socket events
 * Use this as a wrapper for event handlers that require authentication
 */
export function requireAuth<T extends any[]>(
  handler: (socket: AuthenticatedSocket, ...args: T) => void
) {
  return (socket: Socket, ...args: T) => {
    if (!isSocketAuthenticated(socket)) {
      logger.warn('Unauthenticated socket attempted to access protected event', {
        socketId: socket.id,
        event: handler.name
      });
      socket.emit('error', {
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
      return;
    }

    handler(socket as AuthenticatedSocket, ...args);
  };
}

/**
 * Helper to validate room access for authenticated user
 * @param user - Authenticated user
 * @param roomId - Room ID to validate
 * @returns true if user has access to the room
 */
export function validateRoomAccess(user: AuthenticatedUser, roomId: string): boolean {
  // Check if the user's token contains the correct room ID
  if (user.roomId !== roomId) {
    logger.warn('User attempted to access unauthorized room', {
      userId: user.userId,
      tokenRoomId: user.roomId,
      requestedRoomId: roomId
    });
    return false;
  }

  return true;
}

/**
 * Helper to validate game access for authenticated user
 * @param user - Authenticated user
 * @param gameId - Game ID to validate
 * @returns true if user has access to the game
 */
export function validateGameAccess(user: AuthenticatedUser, gameId: string): boolean {
  // Check if the user's token contains the correct game ID
  if (user.gameId !== gameId) {
    logger.warn('User attempted to access unauthorized game', {
      userId: user.userId,
      tokenGameId: user.gameId,
      requestedGameId: gameId
    });
    return false;
  }

  return true;
}
