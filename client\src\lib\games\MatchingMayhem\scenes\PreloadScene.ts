import * as Phaser from 'phaser';
import { GAME_CONFIG } from '../config/GameConfig';
import { gameActions } from '$lib/stores';

export default class PreloadScene extends Phaser.Scene {
  // 2D array to store animal images by [animal][color]
  private animalImages: string[][] = [];

  constructor() {
    super('PreloadScene');
  }

  preload(): void {
    
    // Update progress bar as assets are loaded
    this.load.on('progress', (value: number) => {
      // progressBar.width = bgBar.width * value;
      gameActions.updateLoadingProgress(value);
    });

    // Remove progress bar when complete
    this.load.on('complete', () => {
      // progressBar.destroy();
      // bgBar.destroy();
      // loadingText.destroy();
      gameActions.preloadComplete();
    });

    // Load UI assets
    this.load.image('card_bg', '/assets-matching-mayhem/images/card_bg.svg');
    this.load.image('card_correct_bg', '/assets-matching-mayhem/images/card_correct_bg.png');
    this.load.image('card_incorrect_bg', '/assets-matching-mayhem/images/card_incorrect_bg.png');

    // Load timer assets for the new design
    this.load.image('timer_bg', '/assets/images/timer_bg.svg');
    this.load.image('timer_icon', '/assets/images/timer_icon.png');
    this.load.image('timer_countdown_bg', '/assets/images/timer_countdown_bg.png');

    this.load.svg('heart', '/assets/images/mdi--heart.svg');
    this.load.svg('heart_outline', '/assets/images/mdi-light--heart.svg');
    this.load.svg('heart_broken', '/assets/images/mdi--heart-broken.svg');

    // Load new UI assets for the updated design
    this.load.image('game_name', '/assets-matching-mayhem/images/game_name.svg');
    this.load.image('game_background', '/assets/images/game_bg.png')
    this.load.image('button_bg', '/assets/images/button_bg.svg');
    this.load.image('game_start', '/assets/images/game_start.png');
    this.load.image('game_over', '/assets/images/game_over.svg');
    this.load.image('back_to_lobby', '/assets/images/back_to_lobby.png');


    // Load card images - we have 3 color variations (cyan, green, yellow) of the same animals
    // Each index represents the same animal in a different color
    // 0: cyan, 1: green, 2: yellow

    // Initialize the 2D array
    for (let animalIndex = 0; animalIndex < GAME_CONFIG.ANIMAL_COUNT; animalIndex++) {
      this.animalImages[animalIndex] = [];
    }

    // Load images and store their keys in the 2D array
    for (let animalIndex = 0; animalIndex < GAME_CONFIG.ANIMAL_COUNT; animalIndex++) {
      for (let colorCategory = 0; colorCategory < GAME_CONFIG.COLOR_COUNT; colorCategory++) {
        const imageKey = `image_${colorCategory}_${animalIndex}`;
        this.load.image(imageKey, `/assets-matching-mayhem/images/${colorCategory}/${animalIndex}.png`);
        this.animalImages[animalIndex][colorCategory] = imageKey;
      }
    }

    // Load audio - common sounds from centralized location
    this.load.audio('countdown', ['/assets/audio/countdown.mp3', '/assets/audio/countdown.wav']);
    this.load.audio('go', ['/assets/audio/go.mp3', '/assets/audio/go.wav']);
    this.load.audio('wrong', ['/assets/audio/wrong.mp3', '/assets/audio/wrong.wav']);

    // Load game-specific audio
    this.load.audio('end', ['/assets-matching-mayhem/sounds/end.mp3', '/assets-matching-mayhem/sounds/end.wav']);
    this.load.audio('correct', ['/assets-matching-mayhem/sounds/correct.mp3', '/assets-matching-mayhem/sounds/correct.wav']);
    this.load.audio('round', ['/assets-matching-mayhem/sounds/round.mp3', '/assets-matching-mayhem/sounds/round.wav']);
    this.load.audio('laser', ['/assets-matching-mayhem/sounds/laser.mp3', '/assets-matching-mayhem/sounds/laser.wav']);

    // Load countdown images
    this.load.image('countdown-3', '/assets/images/countdown-3.png');
    this.load.image('countdown-2', '/assets/images/countdown-2.png');
    this.load.image('countdown-1', '/assets/images/countdown-1.png');
    this.load.image('countdown-go', '/assets/images/countdown-go.png');

    // Load font
    this.load.bitmapFont('game_font', '/assets-matching-mayhem/fonts/font.png', '/assets-matching-mayhem/fonts/font.xml');
  }

  /**
   * Get the image key for a specific animal and color
   * @param animalIndex The index of the animal (0-3)
   * @param colorIndex The index of the color (0-2)
   * @returns The image key
   */
  getAnimalImageKey(animalIndex: number, colorIndex: number): string {
    if (animalIndex >= 0 && animalIndex < this.animalImages.length &&
        colorIndex >= 0 && colorIndex < this.animalImages[animalIndex].length) {
      return this.animalImages[animalIndex][colorIndex];
    }
    console.error(`Invalid animal or color index: ${animalIndex}, ${colorIndex}`);
    return '';
  }

  create(): void {
    // Store the animal images in the registry so other scenes can access them
    this.game.registry.set('animalImages', this.animalImages);

    // // Wait a moment before transitioning to ensure assets are properly processed
    // this.time.delayedCall(500, () => {
    //   // Set the default background color to match image 2
    //   this.cameras.main.setBackgroundColor('#0E0F1E');

    //   this.scene.start('GameStartScene');
    // });
  }
}
