import type { TicTapsMessage } from '../types/types';

/**
 * TicTapsConnector - Handles communication with TicTaps platform
 * Equivalent to the TicTaps class in the Unity version
 */
export default class TicTapsConnector {
  private isWebGL: boolean;

  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }

  /**
   * Check if running in a browser environment and embedded
   */
  private checkIfWebGL(): boolean {
    return (typeof window !== 'undefined' && window.parent && window.parent !== window);
  }

  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
  */
  private hasNotifiedReady = false;

  public notifyGameReady(): void {
    console.log('notifyGameReady -- ENTER');
    if (this.hasNotifiedReady) return;
    this.hasNotifiedReady = true;
    
    console.log('Notifying game ready');
    this.sendMessage({ type: 'gameReady' });
  }

  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  public sendScore(score: number): void {
    this.sendMessage({ type: 'gameScore', score });
  }

  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  public notifyGameQuit(): void {
    this.sendMessage({ type: 'gameQuit' });
  }

    /**
   * Send a typed message to the parent window
   */
  private sendMessage(message: TicTapsMessage): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage(message, '*');
      console.log('Message sent to parent:', message);
    }
  }
}
