# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

All development commands should be run from the `client/` directory:

```bash
cd client/
pnpm install          # Install dependencies
pnpm run dev          # Start development server
pnpm run build        # Build for production
pnpm run preview      # Preview production build
pnpm run check        # Run Svelte type checking
pnpm run check:watch  # Run type checking in watch mode
```

## Project Architecture

This is a **TicTaps Games** project - a SvelteKit-based game client that runs in an iframe within a PWA. The architecture follows this pattern:

```
PWA (Parent) --> iframe postMessage --> GameApp (This Client)
GameApp --> Socket.IO --> Node.js Game Server (planned)
Game Server --> Auth/Score APIs --> Python Backend (planned)
```

### Key Communication Patterns

1. **PWA Integration**: Receives data via postMessage API including auth tokens, submit score IDs, room IDs, opponent scores, and game IDs
2. **Socket.IO**: Real-time multiplayer communication (client ready, server planned)
3. **Phaser 3**: Game engine integration with 4 existing games

### Game Structure

Each game follows a consistent structure under `src/lib/games/[GameName]/`:
- `index.ts` - Game class with init/start/pause/resume/destroy methods
- `scenes/` - Phaser 3 scenes (PreloadScene, GameStartScene, GameScene, GameEndScene)
- `managers/` - Game logic managers (ScoreManager, TimerManager, LivesManager)
- `objects/` - Game-specific Phaser objects
- `config/` - Game configuration
- `types/` - TypeScript type definitions
- `ui/` - Custom UI components
- `utils/` - Game utilities including TicTapsConnector

### Available Games

- **finger-frenzy**: Fast-paced finger tapping game
- **bingo**: Classic bingo with multiplayer features  
- **matching-mayhem**: Memory/matching card game
- **number-sequence**: Pattern recognition and sequence game

## Core Components

### Generic UI Components (`src/lib/components/`)
- `GameHUD.svelte` - Universal HUD showing score, time, lives, opponent score
- `Countdown.svelte` - Shared countdown timer for all games
- `EndGame.svelte` - Generic end game screen
- `StartScreen.svelte` - Game start screen
- `Preloading.svelte` - Loading screen component

### Game State Management (`src/lib/stores/`)
- Centralized game state using Svelte stores
- Actions for score updates, game lifecycle, opponent tracking

### Communication Layer
- `src/lib/socket/client.ts` - Socket.IO client for real-time multiplayer (server not yet implemented)
- `src/lib/utils/postMessage.ts` - PostMessage handler for PWA iframe communication

## Game Integration Pattern

Games are integrated through a factory pattern in `src/lib/games/index.ts`. Each game class must implement:
- `init()` - Initialize Phaser 3 game instance
- `start()` - Start game logic
- `pause()/resume()` - Pause/resume functionality  
- `destroy()` - Clean up resources
- `getCurrentScore()` - Return current score

Games receive callbacks for:
- `onScoreUpdate(score)` - Called when score changes
- `onGameComplete(finalScore)` - Called when game ends

## File Structure Context

- `client/` - Main SvelteKit application
- `client/static/assets-[game]/` - Game-specific assets (audio, images, fonts)
- `client/static/assets/` - Shared assets across all games
- `client/src/routes/game/[id]/` - Dynamic routing for game IDs
- `task.md` - Comprehensive project tasks and development roadmap

## Technical Stack

- **Frontend**: SvelteKit 2.x with TypeScript
- **Game Engine**: Phaser 3.90.0
- **Styling**: Tailwind CSS 4.x
- **Real-time**: Socket.IO client (server planned)
- **Package Manager**: pnpm

## Important Notes

- This is the client-side component only; the Node.js game server is planned but not yet implemented
- All games run within a shared UI framework with generic components
- PostMessage communication is set up for PWA integration
- Socket.IO client exists but connects to a planned server
- Static assets are organized per-game for better modularity