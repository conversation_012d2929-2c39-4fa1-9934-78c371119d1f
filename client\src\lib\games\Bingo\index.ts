import * as Phaser from 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';
import type { SocketClient } from '$lib/socket';

// Bingo game integration
export interface BingoConfig {
	gameId: string;
	containerId: string;
	socketClient?: SocketClient;
	roomId?: string;

	onScoreUpdate?: (score: number) => void;
	onGameComplete?: (finalScore: number) => void;
}

export class BingoGame {
	private config: BingoConfig;
	private gameInstance: Phaser.Game | null = null;

	constructor(config: BingoConfig) {
		this.config = config;

		const _config: Phaser.Types.Core.GameConfig = {
			type: Phaser.AUTO,
			width: 540,
			height: 960,
			backgroundColor: '#0E0F1E',
			parent: config.containerId,
			scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
			physics: {
			default: 'arcade',
			arcade: {
				gravity: { x: 0, y: 0 },
				debug: false
			}
			},
			scale: {
				mode: Phaser.Scale.EXPAND,
				autoCenter: Phaser.Scale.CENTER_BOTH,
			},
			// Mobile optimizations
			render: {
				antialias: true,
				powerPreference: 'high-performance'  // Request more GPU power
			},
			input: {
				activePointers: 3,  // Allow multiple touch points
				windowEvents: false // Prevent window event conflicts
			},
			dom: {
				createContainer: true // Enable DOM support if needed
			}
		};

		this.gameInstance = new Phaser.Game(_config);

		// Store config in game registry for access by scenes
		this.gameInstance.registry.set('gameConfig', this.config);
	}

	async init() {
		console.log('Initializing Bingo game...');
		// TODO: Implement Phaser 3 game initialization
	}

	start() {
		console.log('Starting Bingo game...');
		// TODO: Start game logic

		this.gameInstance?.scene.start('GameScene');
	}

	pause() {
		console.log('Pausing Bingo game...');
		// TODO: Pause game logic
	}

	resume() {
		console.log('Resuming Bingo game...');
		// TODO: Resume game logic
	}

	destroy() {
		console.log('Destroying Bingo game...');
		if (this.gameInstance) {
			this.gameInstance.destroy(true);
			this.gameInstance = null;
		}
	}

	getCurrentScore(): number {
		// TODO: Return current game score
		return 0;
	}
}