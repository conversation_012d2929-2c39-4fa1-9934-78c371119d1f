<script lang="ts">
  interface Props {
    progress: number;
  }

  let { progress }: Props = $props();

  // export let progress: number = 0; // Progress value from 0 to 1

  // Hide the loading screen when progress reaches 1
  let isComplete = $derived(progress >= 1);
  // $: isComplete = progress >= 1;
</script>

{#if !isComplete}
  <div
    class="absolute w-screen h-screen z-1000 flex flex-col justify-center items-center"
  >
    <!-- Background -->
    <div class="background"></div>

    <!-- Loading Text -->
    <!-- <p class="text-2xl text-white mb-8">Loading...</p> -->

    <!-- Progress Bar Container -->
    <div class="w-80 h-4 bg-gray-700 rounded-full overflow-hidden">
      <!-- Progress Bar Fill -->
      <div
        class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"
        style="width: {Math.max(0, Math.min(100, progress * 100))}%"
      ></div>
    </div>

    <!-- Progress Percentage -->
    <p class="text-sm text-gray-300 mt-4">
      {Math.round(Math.max(0, Math.min(100, progress * 100)))}%
    </p>
  </div>
{/if}

<style>
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("/assets/images/game_bg.png") no-repeat center center;
    background-size: cover;
    z-index: -1;
  }
</style>
