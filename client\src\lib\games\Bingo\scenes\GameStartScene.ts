import * as Phaser from 'phaser';
import TicTapsConnector from '../utils/TicTapsConnector';

export default class GameStartScene extends Phaser.Scene {
  private ticTaps!: TicTapsConnector;
  private startButton!: Phaser.GameObjects.Image;
  private isStarting: boolean = false;

  constructor() {
    super('GameStartScene');
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Initialize TicTaps connector
    this.ticTaps = new TicTapsConnector();

    // Background
    this.add.image(0, 0, 'game_background')
      .setOrigin(0, 0)
      .setDisplaySize(width, height);

    // Game Title
    const gameTitle = this.add.image(
      width / 2,
      height * 0.25, // Positioned at about 25% from the top
      'game_name'
    ).setOrigin(0.5);

    // Scale the title to fit nicely on the screen
    const titleScale = 
      // (width * 0.6) / gameTitle.width;
      Math.min((width * 0.7) / gameTitle.width, 0.8); // Limit max scale to 0.8
    gameTitle.setScale(titleScale);

    // Add a subtle pulse animation to the game title
    this.tweens.add({
      targets: gameTitle,
      scaleX: titleScale * 1.02,
      scaleY: titleScale * 1.02,
      duration: 1500,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    });

    // Start Button
    this.startButton = this.add.image(
      width / 2,
      height * 0.6, // Positioned at about 60% from the top
      'button_bg'
    ).setOrigin(0.5);

    // Scale the button to match the design
    const buttonScale = 
      // (width * 0.6) / this.startButton.width;
      Math.min((width * 0.6) / this.startButton.width, 0.4); // Limit max scale to 1.0
    this.startButton.setScale(buttonScale);

    // Start Text
    const startText = this.add.image(
      this.startButton.x,
      this.startButton.y - 5,
      'game_start'
    ).setOrigin(0.5);

    // Scale the start text to fit inside the button
    const textScale = (this.startButton.displayWidth * 0.6) / startText.width;
    startText.setScale(textScale);

    // Make button interactive with hover effects
    this.startButton.setInteractive({ useHandCursor: true });

    // Add hover effects
    this.startButton.on('pointerover', () => {
      // Scale up slightly on hover
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 1.05,
        scaleY: buttonScale * 1.05,
        duration: 150,
        ease: 'Sine.easeOut'
      });
    });

    this.startButton.on('pointerout', () => {
      // Scale back to normal on pointer out
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale,
        scaleY: buttonScale,
        duration: 150,
        ease: 'Sine.easeOut'
      });
    });

    // Add click animation
    this.startButton.on('pointerdown', () => {
      // Play a sound effect if available
      if (this.sound.get('countdown')) {
        this.sound.play('countdown', { volume: 0.7 });
      }

      // Notify TicTaps that the game is ready when start is clicked
      this.ticTaps.notifyGameReady();

      // Scale down slightly when clicked
      this.tweens.add({
        targets: [this.startButton, startText],
        scaleX: buttonScale * 0.95,
        scaleY: buttonScale * 0.95,
        duration: 100,
        yoyo: true,
        onComplete: () => this.startGameCountdown(width, height)
      });
    });
  }

  private startGameCountdown(width: number, height: number): void {
    if (this.isStarting) return;
    this.isStarting = true;

    // Create a white flash effect that covers the entire screen
    const flash = this.add.rectangle(
      width / 2, 
      height / 2,
      width,
      height,
      0xffffff
    ).setAlpha(0).setOrigin(0.5);

    // Make sure flash is on top of everything
    flash.setDepth(1000);

    // Flash in
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: 'Sine.easeOut',
      onComplete: () => {
        // Play transition sound if available
        if (this.sound.get('go')) {
          this.sound.play('go', { volume: 0.7 });
        }

        // Hold the flash briefly then fade it out
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50, // Hold for 50ms
          duration: 250,
          ease: 'Sine.easeIn',
          onComplete: () => {
            // Start the game scene
            this.scene.start('GameScene');
          }
        });
      }
    });
  }
}
