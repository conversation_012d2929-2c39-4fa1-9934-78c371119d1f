import { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import { GameState, GameAction, GameInitResult, GameStartData } from '../../types/game';
import {
  NumberSequenceGameState,
  SequenceSelectActionData,
  NumberSequenceGameStartedData,
  RoundData
} from '../../types/numberSequence';
import { submitGameScore, ScoreSubmissionData } from '../../utils/externalApi';

export default class NumberSequenceController {
  private gameService: GameService;
  private gameStates: Map<string, NumberSequenceGameState> = new Map();
  private socketMap: Map<string, Socket> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize a new Number Sequence game session (called at client load)
   */
  initializeGame(roomId: string, socket: Socket): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId);

    if (gameState) {
      // Game state exists, check if we can initialize
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      // If game ended, clean up and create new state to allow restart
      if (gameState.status === 'ended') {
        this.cleanup(roomId);
        gameState = this.gameService.createGameState(roomId, GAME_TYPES.NUMBER_SEQUENCE, 3);
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(roomId, GAME_TYPES.NUMBER_SEQUENCE, 3);
    }

    // Initialize Number Sequence specific game state
    const initialState: NumberSequenceGameState = {
      gameType: 'numbers',
      status: 'initialized',
      score: 0,
      scoreAry: [],
      lives: 3,
      currentSequence: this.generateSequence(1),
      currentIndex: 0,
      streak: 0,
      currentLevel: 1,
      isRoundActive: false,
      roundStartTime: 0,
      roundsCompleted: 0
    };

    this.gameStates.set(roomId, initialState);
    this.socketMap.set(roomId, socket);

    logger.info(`Number Sequence game initialized for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Start the game (called after countdown ends)
   */
  startGame(roomId: string, socket: Socket): GameInitResult {
    const gameState = this.gameService.getGameState(roomId);
    const numberSequenceState = this.gameStates.get(roomId);

    if (!gameState) {
      return { success: false, message: 'Game not initialized. Call initializeGame first.' };
    }

    if (!numberSequenceState) {
      return { success: false, message: 'Game data not found. Call initializeGame first.' };
    }

    if (gameState.status === 'active') {
      return { success: false, message: 'Game is already active' };
    }

    // If game ended, clean up and allow restart
    if (gameState.status === 'ended') {
      this.cleanup(roomId);
      return { success: false, message: 'Game ended, please reinitialize' };
    }

    // Start the game using GameService
    const started = this.gameService.startGame(roomId, socket);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Update Number Sequence state to active
    numberSequenceState.status = 'active';
    numberSequenceState.isRoundActive = true;
    numberSequenceState.roundStartTime = Date.now();

    logger.info(`Number Sequence game started for room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Initialize and start a new Number Sequence game session (legacy method for backward compatibility)
   * @deprecated Use initializeGame() followed by startGame() instead
   */
  private initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    const initResult = this.initializeGame(roomId, socket);
    if (!initResult.success) {
      return initResult;
    }

    return this.startGame(roomId, socket);
  }

  /**
   * Generate a sequence for the given level
   */
  private generateSequence(level: number): number[] {
    const length = Math.min(3 + level, 10); // Increase sequence length with level, max 10
    const sequence: number[] = [];
    
    for (let i = 0; i < length; i++) {
      sequence.push(Math.floor(Math.random() * 9) + 1); // Numbers 1-9
    }
    
    return sequence;
  }

  /**
   * Handle game initialization event (called at client load)
   */
  handleGameInit(socket: Socket, data: GameStartData): void {
    const { roomId, gameId, submitScoreId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'initialization');
      return;
    }

    // Store submitScoreId in game session for later use
    if (submitScoreId) {
      console.log(`Number Sequence game session ${roomId} - submitScoreId: ${submitScoreId}`);
      // TODO: Store submitScoreId in game session data for score submission
    }

    try {
      // Initialize the game (but don't start it yet)
      const result = this.initializeGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get current sequence for the client
        const gameState = this.gameStates.get(roomId);
        const currentSequence = gameState?.currentSequence || [];

        socket.emit('initialized', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          currentSequence,
          currentLevel: gameState?.currentLevel || 1,
          message: 'Game initialized!'
        });

        logger.info(`${gameId} game initialized in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to initialize game', 'initialization');
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'initialization');
    }
  }

  /**
   * Handle game start event (called after countdown)
   */
  private handleGameStart(socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId or gameId', 'start');
      return;
    }

    try {
      // Start the game (game should already be initialized)
      const result = this.startGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get current sequence for the client
        const gameState = this.gameStates.get(roomId);

        const gameStartedData: NumberSequenceGameStartedData = {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime || Date.now()
          },
          currentSequence: gameState?.currentSequence || [],
          currentLevel: gameState?.currentLevel || 1,
          message: 'Game started!'
        };

        socket.emit('started', gameStartedData);
        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to start game', 'start');
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error', 'start');
    }
  }

  /**
   * Emit a fatal error that should stop the game
   */
  private emitFatalError(socket: Socket, roomId: string, message: string, errorType: string): void {
    socket.emit('game_fatal_error', {
      message,
      errorType,
      roomId,
      timestamp: Date.now()
    });
  }

  /**
   * Handle number selection action
   */
  private async handleSequenceSelectAction(socket: Socket, data: SequenceSelectActionData): Promise<void> {
    const { roomId } = data;
    const { selectedNumber } = data.action.data;

    if (!roomId) {
      socket.emit('error', {
        message: 'Missing roomId for sequence select action'
      });
      return;
    }

    try {
      const gameState = this.gameStates.get(roomId);
      if (!gameState) {
        socket.emit('error', {
          message: 'No game state found for this room'
        });
        return;
      }

      const isCorrect = selectedNumber === gameState.currentSequence[gameState.currentIndex];
      
      if (isCorrect) {
        gameState.currentIndex++;
        gameState.score += 10 * gameState.currentLevel;
        gameState.scoreAry.push(gameState.score);

        // Check if sequence is completed
        if (gameState.currentIndex >= gameState.currentSequence.length) {
          // Level complete
          gameState.currentLevel++;
          gameState.currentSequence = this.generateSequence(gameState.currentLevel);
          gameState.currentIndex = 0;
          gameState.roundsCompleted++;
          gameState.streak++;
        }

        socket.emit('action_result', {
          success: true,
          isCorrect: true,
          newScore: gameState.score,
          currentIndex: gameState.currentIndex,
          nextSequence: gameState.currentIndex === 0 ? gameState.currentSequence : undefined,
          currentLevel: gameState.currentLevel
        });
      } else {
        // Wrong number selected
        gameState.lives--;
        gameState.streak = 0;

        if (gameState.lives <= 0) {
          // Game over
          const results = this.gameService.endGame(roomId, 'no_lives');

          // Submit score to external API
          await this.submitScore(roomId, gameState, 'no_lives');

          if (results) {
            socket.emit('ended', results);
          }
          return;
        }

        // Reset current sequence
        gameState.currentIndex = 0;
        socket.emit('action_result', {
          success: true,
          isCorrect: false,
          newScore: gameState.score,
          currentIndex: gameState.currentIndex,
          livesRemaining: gameState.lives
        });
      }

      this.gameStates.set(roomId, gameState);
    } catch (error) {
      logger.error(`Error processing sequence select action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: any): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      switch (action.type) {
        case 'sequence_select':
          this.handleSequenceSelectAction(socket, data as SequenceSelectActionData);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Submit score to external API
   */
  private async submitScore(roomId: string, gameState: any, reason: string, submitScoreId?: string, authToken?: string): Promise<void> {
    try {
      const scoreData: ScoreSubmissionData = {
        roomId,
        score: gameState.score,
        scoreArray: gameState.scoreAry || [],
        submitScoreId,
        authToken
      };

      const result = await submitGameScore(scoreData);

      if (result.success) {
        logger.info(`Score submitted successfully for Number Sequence game in room ${roomId}`);
      } else {
        logger.warn(`Score submission failed for Number Sequence game in room ${roomId}: ${result.error}`);
      }
    } catch (error) {
      logger.error(`Error submitting score for Number Sequence game in room ${roomId}:`, error);
    }
  }

  /**
   * Handle game end
   */
  private async handleGameEnd(socket: Socket, data: any): Promise<void> {
    const { roomId } = data;

    if (!roomId) {
      socket.emit('error', {
        message: 'Missing roomId for game end'
      });
      return;
    }

    try {
      const gameState = this.gameStates.get(roomId);
      const results = this.gameService.endGame(roomId, 'manual');

      // Submit score to external API
      if (gameState) {
        await this.submitScore(roomId, gameState, 'manual');
      }

      if (results) {
        socket.emit('ended', results);
      }

      // Cleanup
      this.cleanup(roomId);
    } catch (error) {
      logger.error(`Error ending game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Setup socket event handlers for Number Sequence
   */
  public setupSocketHandlers(_io: Server, socket: Socket): void {
    // Generic game init event
    socket.on('init', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameInit(socket, data);
      }
    });

    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.NUMBER_SEQUENCE) {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Clean up game resources
   */
  public cleanup(roomId: string): void {
    this.gameStates.delete(roomId);
    this.socketMap.delete(roomId);

    // Delete game state from GameService to allow restarts
    this.gameService.deleteGameState(roomId);
  }
}
