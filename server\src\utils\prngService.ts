import { createHmac } from "crypto"; // Importing the necessary module

interface ByteGeneratorParams {
  serverSeed: string;
  clientSeed: string;
  nonce: number;
  cursor: number;
}

// Random number generation based on following inputs: serverSeed, clientSeed, nonce, and cursor
function* byteGenerator({ serverSeed, clientSeed, nonce, cursor }: ByteGeneratorParams): Generator<number> {
  // Setup cursor variables
  let currentRound = Math.floor(cursor / 32);
  let currentRoundCursor = cursor % 32; // Fixing the cursor calculation

  // Generate outputs until cursor requirement fulfilled
  while (currentRoundCursor < 32) {
    // Adding a termination condition
    // HMAC function used to output provided inputs into bytes
    const hmac = createHmac("sha256", serverSeed);
    hmac.update(`${clientSeed}:${nonce}:${currentRound}`);
    const buffer = hmac.digest();

    // Update cursor for next iteration of loop
    yield Number(buffer[currentRoundCursor]);
    currentRoundCursor += 1;
    if (currentRoundCursor === 32) {
      currentRound += 1;
      currentRoundCursor = 0;
    }
  }
}

// Convert the hash output from the RNG byteGenerator to floats
export function generateFloats(
  serverSeed: string,
  clientSeed: string,
  nonce: number,
  cursor: number,
  count: number
): number[] {
  // Random number generator function
  const rng = byteGenerator({ serverSeed, clientSeed, nonce, cursor });
  // Declare bytes as empty array
  const bytes: number[] = [];

  // Populate bytes array with sets of 4 from RNG output
  while (bytes.length < count * 4) {
    const next = rng.next();
    if (next.value !== undefined) {
      bytes.push(next.value);
    }
  }

  // Return bytes as floats using reduce function
  return Array.from({ length: count }, (_, index) =>
    bytes.slice(index * 4, (index + 1) * 4).reduce((result, value, i) => {
      const divider = 256 ** (i + 1);
      const partialResult = value / divider;
      return result + partialResult;
    }, 0)
  );
}
