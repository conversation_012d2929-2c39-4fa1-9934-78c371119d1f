<script lang="ts">
  import Icon from "@iconify/svelte";

  interface Props {
    score: number;
    time: number;
    totalTime: number;
    lives: number;
    maxLives: number;
    opponentScore: number | null;
    showOpponent: boolean;
  }

  let {
    score,
    time,
    totalTime,
    lives,
    maxLives,
    opponentScore,
    showOpponent,
  }: Props = $props();

  // export let score: number;
  // export let time: number;
  // export let lives: number;
  // export let maxLives: number;
  // export let opponentScore: number | null = null;
  // export let showOpponent: boolean = false;
</script>

<div
  class="fixed top-[3vh] left-0 right-0 z-10 px-[4vw] py-[3vh] flex flex-col items-center justify-around
  gap-[1vh] text-white"
>
  <!-- Timer Section -->
  <div class="relative w-full flex items-center gap-2 bg-black/20">
    <!-- Timer Icon -->
    <div
      class="absolute left-0 top-1/2 -translate-y-1/2 w-[6vh] h-[6vh] flex items-center
      justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10"
    >
      <Icon height="3vh" color="white" icon="material-symbols:timer" />
    </div>

    <!-- Time Display -->
    <div
      class="absolute right-0 top-1/2 -translate-y-1/2 w-[6vh] h-[6vh] flex items-center
      justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10 font-medium text-[2.5vh]"
    >
      {time}s
    </div>

    <!-- Timer Bar -->
    <div class="relative w-full h-6 rounded-xl overflow-hidden">
      <!-- Gradient Bar -->
      <div
        class="absolute top-0 left-0 w-full h-full bg-gradient-to-r
        from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"
        style="width: {(time / totalTime) * 100}%;"
      ></div>
    </div>
  </div>

  <!-- Lives Section -->
  <div class="flex gap-1">
    {#each Array(maxLives) as _, i (i)}
      {#if i < lives}
        <Icon height="4vh" color="red" icon="material-symbols:favorite" />
      {:else}
        <Icon
          height="4vh"
          color="red"
          icon="material-symbols:favorite-outline"
        />
      {/if}
      <!-- <Icon color="red" icon="material-symbols:heart-broken" /> -->
    {/each}
  </div>

  <!-- Score Section -->
  <div class="flex flex-col items-center">
    <span class="text-[2vh] font-medium">Total Point</span>
    <div class="font-bold text-[6vh]">{score}</div>
  </div>
</div>
