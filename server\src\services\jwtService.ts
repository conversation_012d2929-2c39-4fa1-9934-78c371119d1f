import jwt from 'jsonwebtoken';
import { AuthenticatedUser } from '../types/game.js';
import { logger } from '../utils/logger.js';

/**
 * JWT Service for server-side token validation and user data extraction
 * Keeps all JWT operations secure on the server side
 */
export class JwtService {
  private readonly jwtSecret: string;
  private readonly jwtIssuer: string;
  private readonly jwtAudience: string;

  constructor() {
    // Get JWT configuration from environment variables
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
    this.jwtIssuer = process.env.JWT_ISSUER || 'tictaps-games';
    this.jwtAudience = process.env.JWT_AUDIENCE || 'tictaps-games-client';

    if (process.env.NODE_ENV === 'production' && this.jwtSecret === 'your-secret-key-change-in-production') {
      logger.warn('Using default JWT secret in production! Please set JWT_SECRET environment variable.');
    }
  }

  /**
   * Validate and decode a JWT token
   * @param token - The JWT token to validate
   * @returns Decoded user data or null if invalid
   */
  public validateToken(token: string): AuthenticatedUser | null {
    try {
      // Remove 'Bearer ' prefix if present
      const cleanToken = token.replace(/^Bearer\s+/, '');

      // Verify and decode the token
      const decoded = jwt.verify(cleanToken, this.jwtSecret, {
        issuer: this.jwtIssuer,
        audience: this.jwtAudience,
        algorithms: ['HS256']
      }) as any;

      // Extract user data from the decoded token
      const user: AuthenticatedUser = {
        userId: decoded.sub || decoded.userId || decoded.id,
        username: decoded.username,
        email: decoded.email,
        gameId: decoded.gameId,
        roomId: decoded.roomId,
        scoreSubmitId: decoded.scoreSubmitId,
        authToken: decoded.authToken,
        exp: decoded.exp,
        iat: decoded.iat
      };

      // Validate required fields
      if (!user.userId || !user.gameId || !user.roomId || !user.scoreSubmitId) {
        logger.warn('JWT token missing required fields:', {
          hasUserId: !!user.userId,
          hasGameId: !!user.gameId,
          hasRoomId: !!user.roomId,
          hasScoreSubmitId: !!user.scoreSubmitId
        });
        return null;
      }

      logger.info('JWT token validated successfully', {
        userId: user.userId,
        gameId: user.gameId,
        roomId: user.roomId,
        exp: user.exp
      });

      return user;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        logger.warn('Invalid JWT token:', error.message);
      } else if (error instanceof jwt.TokenExpiredError) {
        logger.warn('JWT token expired:', error.message);
      } else if (error instanceof jwt.NotBeforeError) {
        logger.warn('JWT token not active yet:', error.message);
      } else {
        logger.error('JWT validation error:', error);
      }
      return null;
    }
  }

  /**
   * Check if a token is expired without full validation
   * @param token - The JWT token to check
   * @returns true if expired, false if valid or if unable to determine
   */
  public isTokenExpired(token: string): boolean {
    try {
      const cleanToken = token.replace(/^Bearer\s+/, '');
      const decoded = jwt.decode(cleanToken) as any;
      
      if (!decoded || !decoded.exp) {
        return true;
      }

      return Date.now() >= decoded.exp * 1000;
    } catch (error) {
      logger.warn('Error checking token expiration:', error);
      return true;
    }
  }

  /**
   * Extract user ID from token without full validation (for logging purposes)
   * @param token - The JWT token
   * @returns User ID or null
   */
  public extractUserId(token: string): string | null {
    try {
      const cleanToken = token.replace(/^Bearer\s+/, '');
      const decoded = jwt.decode(cleanToken) as any;
      return decoded?.sub || decoded?.userId || decoded?.id || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Generate a JWT token (for testing purposes only)
   * In production, tokens should be generated by the Python backend
   * @param userData - User data to encode
   * @returns JWT token
   */
  public generateToken(userData: Partial<AuthenticatedUser>): string {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Token generation should not be used in production');
    }

    const payload = {
      sub: userData.userId,
      userId: userData.userId,
      username: userData.username,
      email: userData.email,
      gameId: userData.gameId,
      roomId: userData.roomId,
      scoreSubmitId: userData.scoreSubmitId,
      authToken: userData.authToken,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
    };

    return jwt.sign(payload, this.jwtSecret, {
      issuer: this.jwtIssuer,
      audience: this.jwtAudience,
      algorithm: 'HS256'
    });
  }
}

// Singleton instance
export const jwtService = new JwtService();
