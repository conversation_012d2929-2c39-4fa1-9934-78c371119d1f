/**
 * GameConfig - Centralized configuration for game settings
 * This file contains all configurable parameters for the game
 */
export default class GameConfig {
  // Game dimensions
  static readonly GAME_WIDTH: number = 540;
  static readonly GAME_HEIGHT: number = 960;
  static readonly BACKGROUND_COLOR: string = '#0E0F1E';

  // Grid configuration
  static readonly GRID_SIZE: number = 4;
  static readonly INITIAL_ACTIVE_BLOCKS: number = 3;
  static readonly BLOCK_CORNER_RADIUS: number = 10;

  // Timer settings
  static readonly GAME_DURATION: number = 30; // in seconds
  static readonly COOLDOWN_DURATION: number = 0.5; // in seconds
  
  static readonly TMER_INCREASE = 0.2; // in seconds
  static readonly TMER_PUNISHMENT = 1; // in seconds

  static readonly COUNTDOWN_DURATION: number = 3; // in seconds
  static readonly TRANSITION_DURATION: number = 300; // in milliseconds

  // Scoring configuration
  static readonly SCORE_TIERS: { [key: string]: number } = {
    FAST: 5,    // < 500ms
    MEDIUM_FAST: 4, // < 1000ms
    MEDIUM: 3,  // < 1500ms
    MEDIUM_SLOW: 2, // < 2000ms
    SLOW: 1     // >= 2000ms
  };
  static readonly SCORE_TIER_THRESHOLDS: { [key: string]: number } = {
    FAST: 500,
    MEDIUM_FAST: 1000,
    MEDIUM: 1500,
    MEDIUM_SLOW: 2000
  };
  static readonly WRONG_CLICK_PENALTY: number = 5;

  // Animation settings
  static readonly BLOCK_ANIMATION_DURATION: number = 50;
  static readonly WRONG_EFFECT_DURATION: number = 200;
  static readonly SCORE_ANIMATION_DURATION: number = 400;
  static readonly FLASH_DURATION: number = 100;

  // UI settings
  static readonly TIMER_BAR_WIDTH_PERCENT: number = 0.8; // 80% of screen width
  static readonly TIMER_BAR_HEIGHT: number = 35;
  static readonly TIMER_BAR_Y_PERCENT: number = 0.07; // 7% from top
  static readonly GRID_CONTAINER_WIDTH_PERCENT: number = 0.8; // 80% of screen width
  static readonly GRID_CONTAINER_HEIGHT_RATIO: number = 1.4; // height = width * 1.4
  static readonly GRID_CONTAINER_Y_PERCENT: number = 0.63; // 63% from top
  static readonly GRID_GAP_PERCENT: number = 0.03; // 3% gap between cells

  // Input settings
  static readonly MAX_ACTIVE_POINTERS: number = 1; // Single touch only to prevent multi-touch conflicts

  // Colors
  static readonly TIMER_GRADIENT_COLORS: string[] = ['#33DDFF', '#664DFF']; // Cyan to purple
  static readonly SCORE_GRADIENT_COLORS: string[] = ['#4cffae', '#32c4ff', '#5c67ff']; // Green to blue
  static readonly BUTTON_GRADIENT_COLORS: string[] = ['#32c4ff', '#7f54ff', '#b63efc']; // Cyan to purple to pink
  static readonly WARNING_COLOR: string = '#ff0000'; // Red for warnings/errors

  // Sound settings
  static readonly SOUND_VOLUME: number = 0.7;

  // Asset keys
  static readonly ASSETS = {
    IMAGES: {
      BLOCK_ACTIVE: 'block_active',
      BLOCK_INACTIVE: 'block_inactive',
      GAME_START: 'game_start',
      GAME_NAME: 'game_name',
      TIMER_BG: 'timer_bg',
      TIMER_ICON: 'timer_icon',
      TIMER_COUNTDOWN_BG: 'timer_countdown_bg',
      COUNTDOWN_3: 'countdown-3',
      COUNTDOWN_2: 'countdown-2',
      COUNTDOWN_1: 'countdown-1',
      COUNTDOWN_GO: 'countdown-go',
      BUTTON_BG: 'button_bg',
      GAME_OVER: 'game_over',
      BACK_TO_LOBBY: 'back_to_lobby',
      GAME_BACKGROUND: 'game_bg'
    },
    SOUNDS: {
      TAP: 'tap',
      RIGHT: 'right',
      WRONG: 'wrong',
      TIMEOUT: 'timeout',
      CLICK: 'click',
      COUNTDOWN: 'countdown',
      GO: 'go'
    }
  };

  // Debug settings
  static readonly DEBUG_MODE: boolean = true;
  static readonly SHOW_FPS: boolean = false;
}
