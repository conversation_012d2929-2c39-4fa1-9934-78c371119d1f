import { Server, Socket } from 'socket.io';
import { FingerF<PERSON>zy<PERSON><PERSON>roller, BingoController, MatchingMayhemController, NumberSequenceController } from '../controllers';
import gameService from '../services/gameService';
import { authMiddleware, AuthenticatedSocket, getAuthenticatedUser, validateRoomAccess } from '../middleware/auth.js';
import { sessionService } from '../services/sessionService.js';
import { logger } from '../utils/logger.js';

// Initialize game controllers
const fingerFrenzyController = new FingerFrenzyController(gameService);
const bingoController = new BingoController(gameService);
const matchingMayhemController = new MatchingMayhemController(gameService);
const numberSequenceController = new NumberSequenceController(gameService);

export function setupSocketHandlers(io: Server) {
  // Apply authentication middleware
  io.use(authMiddleware);

  io.on('connection', (socket: AuthenticatedSocket) => {
    const user = getAuthenticatedUser(socket);
    if (!user) {
      logger.error('Authenticated socket missing user data', { socketId: socket.id });
      socket.disconnect();
      return;
    }

    logger.info(`Authenticated client connected`, {
      socketId: socket.id,
      userId: user.userId,
      gameId: user.gameId,
      roomId: user.roomId
    });

    // Create user session
    sessionService.createUserSession(user, socket.id);

    // Automatically join the user's designated room
    socket.join(user.roomId);

    // Setup game-specific handlers
    fingerFrenzyController.setupSocketHandlers(io, socket);
    bingoController.setupSocketHandlers(io, socket);
    matchingMayhemController.setupSocketHandlers(io, socket);
    numberSequenceController.setupSocketHandlers(io, socket);

    // Handle room joining (with authentication validation)
    socket.on('join_room', (data) => {
      const { roomId } = data;

      // Validate room access
      if (!validateRoomAccess(user, roomId)) {
        socket.emit('error', {
          message: 'Access denied to room',
          code: 'ROOM_ACCESS_DENIED'
        });
        return;
      }

      // Update user activity
      sessionService.updateUserActivity(user.userId);

      logger.info(`User joining room`, {
        userId: user.userId,
        roomId,
        gameId: user.gameId
      });

      socket.join(roomId);
      socket.emit('room_joined', {
        roomId,
        userId: user.userId,
        gameId: user.gameId,
        message: 'Successfully joined room'
      });

      // Notify other players in the room
      socket.to(roomId).emit('player_joined', {
        userId: user.userId,
        gameId: user.gameId
      });
    });

    // Handle room leaving
    socket.on('leave_room', (data) => {
      const { roomId } = data;

      // Validate room access
      if (!validateRoomAccess(user, roomId)) {
        return;
      }

      logger.info(`User leaving room`, {
        userId: user.userId,
        roomId
      });

      socket.leave(roomId);
      socket.emit('room_left', { roomId, userId: user.userId });

      // Notify other players in the room
      socket.to(roomId).emit('player_left', { userId: user.userId });
    });

    // Handle player ready status
    socket.on('player_ready', (data) => {
      const { roomId, playerId, ready } = data;
      console.log(`Player ${playerId} ready status: ${ready} in room ${roomId}`);
      
      // Broadcast to room
      socket.to(roomId).emit('player_ready_update', { playerId, ready });
    });

    // Handle game actions
    socket.on('game_action', (data) => {
      const { roomId, playerId, action, gameData } = data;
      console.log(`Game action from ${playerId} in room ${roomId}:`, action);
      
      // Broadcast action to other players in the room
      socket.to(roomId).emit('opponent_action', { 
        playerId, 
        action, 
        gameData,
        timestamp: Date.now()
      });
    });

    // Handle score submission
    socket.on('submit_score', (data) => {
      const { roomId, playerId, score, gameType } = data;
      console.log(`Score submission from ${playerId}: ${score} in room ${roomId}`);
      
      // TODO: Validate and submit score to Python backend
      
      // Broadcast score update to room
      socket.to(roomId).emit('score_update', { 
        playerId, 
        score, 
        gameType,
        timestamp: Date.now()
      });
    });

    // Handle game start
    socket.on('start_game', (data) => {
      const { roomId, gameType } = data;
      console.log(`Starting game ${gameType} in room ${roomId}`);
      
      // Broadcast game start to all players in room
      io.to(roomId).emit('game_start', { 
        gameType, 
        startTime: Date.now(),
        message: 'Game has started!'
      });
    });

    // Handle game end
    socket.on('end_game', (data) => {
      const { roomId, gameType, results } = data;
      console.log(`Ending game ${gameType} in room ${roomId}`);
      
      // Broadcast game end to all players in room
      io.to(roomId).emit('game_end', { 
        gameType, 
        results,
        endTime: Date.now(),
        message: 'Game has ended!'
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Authenticated client disconnected`, {
        socketId: socket.id,
        userId: user.userId,
        roomId: user.roomId
      });

      // Get all rooms this socket was in
      const rooms = Array.from(socket.rooms);

      // Clean up game states for each room (excluding the socket's own room)
      rooms.forEach(roomId => {
        if (roomId !== socket.id) {
          logger.info(`Cleaning up room due to disconnect`, {
            roomId,
            userId: user.userId
          });

          // Trigger cleanup in all game controllers
          fingerFrenzyController.cleanup(roomId);
          bingoController.cleanup(roomId);
          matchingMayhemController.cleanup(roomId);
          numberSequenceController.cleanup(roomId);

          // Notify other players in the room about disconnection
          socket.to(roomId).emit('player_disconnected', {
            userId: user.userId,
            socketId: socket.id,
            timestamp: Date.now()
          });
        }
      });

      // Clean up user session
      sessionService.removeUserSessionBySocket(socket.id);
    });

    // Basic message echo for testing
    socket.on('message', (data) => {
      console.log(`Received message: ${data}`);
      socket.emit('message', `Echo: ${data}`);
    });
  });
}
