// Utility functions for communicating with parent PWA via postMessage

export interface PWAMessage {
	type: string;
	data?: any;
}

export interface GameInitData {
	authToken: string;
	submitScoreId: string;
	roomId?: string;
	opponentScore?: number;
	gameId: string;
}

export class PostMessageHandler {
	private listeners: Map<string, Function[]> = new Map();

	constructor() {
		this.setupMessageListener();
	}

	private setupMessageListener() {
		// Only set up listener in browser environment
		if (typeof window !== 'undefined') {
			window.addEventListener('message', (event) => {
				// Verify origin for security (adjust as needed)
				// if (event.origin !== 'pwa-domain') return;

				const message: PWAMessage = event.data;
				this.handleMessage(message);
			});
		}
	}

	private handleMessage(message: PWAMessage) {
		const listeners = this.listeners.get(message.type) || [];
		listeners.forEach(listener => listener(message.data));
	}

	// Listen for specific message types
	on(messageType: string, callback: Function) {
		if (!this.listeners.has(messageType)) {
			this.listeners.set(messageType, []);
		}
		this.listeners.get(messageType)!.push(callback);
	}

	// Remove listener
	off(messageType: string, callback: Function) {
		const listeners = this.listeners.get(messageType) || [];
		const index = listeners.indexOf(callback);
		if (index > -1) {
			listeners.splice(index, 1);
		}
	}

	// Send message to parent PWA
	sendToParent(type: string, data?: any) {
		// Only send messages in browser environment
		if (typeof window !== 'undefined' && window.parent) {
			const message: PWAMessage = { type, data };
			window.parent.postMessage(message, '*');
		}
	}

	// Common message handlers
	onGameInit(callback: (data: GameInitData) => void) {
		this.on('gameInit', callback);
	}

	onPause(callback: () => void) {
		this.on('pause', callback);
	}

	onResume(callback: () => void) {
		this.on('resume', callback);
	}

	// Common message senders
	sendScoreUpdate(score: number) {
		this.sendToParent('scoreUpdate', { score });
	}

	sendGameComplete(finalScore: number, submitScoreId: string) {
		this.sendToParent('gameComplete', { finalScore, submitScoreId });
	}

	sendGameReady() {
		this.sendToParent('gameReady');
	}

	sendError(error: string) {
		this.sendToParent('error', { error });
	}
}

// Singleton instance
export const postMessageHandler = new PostMessageHandler();
