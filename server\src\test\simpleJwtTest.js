// Simple JWT test using CommonJS for compatibility
const jwt = require('jsonwebtoken');

console.log('🧪 Testing JWT Generation...\n');

// Test configuration
const jwtSecret = 'your-secret-key-change-in-production';
const jwtIssuer = 'tictaps-games';
const jwtAudience = 'tictaps-games-client';

// Test data for different game types
const testGameTypes = ['finger-frenzy', 'bingo', 'matching-mayhem', 'numbers'];

function generateToken(userData) {
  const payload = {
    sub: userData.userId,
    userId: userData.userId,
    username: userData.username,
    email: userData.email,
    gameId: userData.gameId,
    roomId: userData.roomId,
    scoreSubmitId: userData.scoreSubmitId,
    authToken: userData.authToken,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
  };

  return jwt.sign(payload, jwtSecret, {
    issuer: jwtIssuer,
    audience: jwtAudience,
    algorithm: 'HS256'
  });
}

function validateToken(token) {
  try {
    const decoded = jwt.verify(token, jwtSecret, {
      issuer: jwtIssuer,
      audience: jwtAudience,
      algorithms: ['HS256']
    });

    return {
      userId: decoded.sub || decoded.userId || decoded.id,
      username: decoded.username,
      email: decoded.email,
      gameId: decoded.gameId,
      roomId: decoded.roomId,
      scoreSubmitId: decoded.scoreSubmitId,
      authToken: decoded.authToken,
      exp: decoded.exp,
      iat: decoded.iat
    };
  } catch (error) {
    console.error('Token validation error:', error.message);
    return null;
  }
}

// Test each game type
for (const gameId of testGameTypes) {
  console.log(`Testing ${gameId}...`);
  
  const userData = {
    userId: `test-user-${Date.now()}`,
    username: `TestPlayer-${gameId}`,
    email: `test-${gameId}@example.com`,
    gameId,
    roomId: `test-room-${gameId}-${Date.now()}`,
    scoreSubmitId: `test-score-${Date.now()}`,
    authToken: `test-auth-${Date.now()}`
  };

  try {
    // Generate token
    const token = generateToken(userData);
    console.log(`✅ Token generated for ${gameId} (${token.length} chars)`);

    // Validate token
    const validatedUser = validateToken(token);
    if (!validatedUser) {
      throw new Error('Token validation failed');
    }

    console.log(`✅ Token validated for ${gameId}`);
    console.log(`   Game ID: ${validatedUser.gameId}`);
    console.log(`   Room ID: ${validatedUser.roomId}`);
    console.log(`   User ID: ${validatedUser.userId}`);

    // Check required fields
    const requiredFields = ['userId', 'gameId', 'roomId', 'scoreSubmitId', 'authToken'];
    const missingFields = requiredFields.filter(field => !validatedUser[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    console.log(`✅ All required fields present for ${gameId}\n`);

  } catch (error) {
    console.error(`❌ Test failed for ${gameId}:`, error.message);
    process.exit(1);
  }
}

console.log('🎉 All JWT generation tests passed!');
console.log('\n📋 Summary:');
console.log('- JWT generation works for all supported game types');
console.log('- Token validation works correctly');
console.log('- All required fields are properly included');
