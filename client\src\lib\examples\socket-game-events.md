### In Game Scenes (e.g., FingerFrenzy GameScene)

```typescript
// Socket client is available via game registry
const gameConfig = this.registry.get("gameConfig") as FingerFrenzyConfig;
this.socketClient = gameConfig?.socketClient || null;

// Send game start event (uses game state for gameId, roomId, submitScoreId)
if (this.socketClient && this.socketClient.isConnected()) {
  this.socketClient.startGame();
}

// Send game action events (uses game state for gameId, roomId)
this.socketClient.sendGameEvent("correctClick", {
  blockIndex: 5,
  reactionTime: 450,
});

// Send specific game actions
this.socketClient.sendTileTap("tile_1_2", 450);
this.socketClient.sendCardSelect("card_animal_red", 320);
this.socketClient.sendNumberSelect(42, 280);

// Send game end event (uses game state for gameId, roomId, submitScoreId)
this.socketClient.endGame("manual");
```
