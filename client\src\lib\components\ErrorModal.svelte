<script lang="ts">
  export let isVisible = false;
  export let errorMessage = "";
  export let errorType = "";
  export let onClose: () => void = () => {};

  function handleClose() {
    // isVisible = false;
    onClose();
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  }
</script>

{#if isVisible}
  <!-- Modal backdrop -->
  <!-- svelte-ignore a11y_interactive_supports_focus -->
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <div
    class="modal-backdrop"
    on:click={handleBackdropClick}
    role="dialog"
    aria-modal="true"
  >
    <!-- Modal container -->
    <div class="modal-container">
      <!-- Blur background for modal -->
      <div class="modal-blur-bg"></div>

      <!-- Modal content -->
      <div class="modal-content">
        <!-- Error icon -->
        <div class="error-icon">
          <svg
            width="64"
            height="64"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="#ff4444"
              stroke-width="2"
              fill="none"
            />
            <path
              d="M15 9l-6 6"
              stroke="#ff4444"
              stroke-width="2"
              stroke-linecap="round"
            />
            <path
              d="M9 9l6 6"
              stroke="#ff4444"
              stroke-width="2"
              stroke-linecap="round"
            />
          </svg>
        </div>

        <!-- Error title -->
        <div class="error-title">
          <h2>Game Error</h2>
        </div>

        <!-- Error type badge -->
        {#if errorType}
          <div class="error-type-badge">
            {errorType.toUpperCase()}
          </div>
        {/if}

        <!-- Error message -->
        <div class="error-message">
          <p>{errorMessage}</p>
        </div>

        <!-- Action buttons -->
        <div class="modal-actions">
          <button class="close-btn" on:click={handleClose}>
            <span>Close</span>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
  }

  .modal-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    min-width: 320px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  }

  .modal-blur-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(28, 29, 34, 0.95) 0%,
      rgba(40, 42, 50, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .modal-content {
    position: relative;
    padding: 32px;
    text-align: center;
    color: white;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  }

  .error-icon {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .error-title h2 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    color: #ff4444;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
  }

  .error-type-badge {
    background: rgba(255, 68, 68, 0.2);
    border: 1px solid #ff4444;
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: bold;
    color: #ff4444;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .error-message {
    max-width: 400px;
    margin: 0 auto;
  }

  .error-message p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
  }

  .modal-actions {
    display: flex;
    gap: 16px;
    margin-top: 20px;
  }

  .close-btn {
    position: relative;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
  }

  .close-btn {
    background: linear-gradient(135deg, #666, #555);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }

  .close-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, #777, #666);
  }

  .close-btn:active {
    transform: translateY(0);
  }

  /* Mobile responsiveness */
  @media (max-width: 480px) {
    .modal-content {
      padding: 24px;
      gap: 16px;
    }

    .error-title h2 {
      font-size: 24px;
    }

    .error-message p {
      font-size: 14px;
    }

    .modal-actions {
      flex-direction: column;
      width: 100%;
    }

    .close-btn {
      width: 100%;
    }
  }
</style>
