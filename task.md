# TicTaps Games - Project Tasks

## Project Architecture Overview

```
PWA -->|auth api| PY
PY -->|auth api| NODE_GAMES
PWA -->|iFrame postMessage: auth token, submit score ID, room ID, opponent score, game ID| GameApp
GameApp -->|SOCKET| NODE_GAMES
NODE_GAMES -->|auth api, submit score api| PY
NODE_GAMES -->|active rooms, scores, timers| REDIS
PY --> DB
```

### Components:

- [x] **[SERVER]** Create `server/` directory structure
- [x] **[SERVER]** Initialize Node.js project with package.json
- [x] **[SERVER]** Set up Express.js server
- [x] **[SERVER]** Configure Socket.IO for real-time communication
- [x] **[SERVER]** Set up in-memory data structures (hash maps for rooms, sessions)
- [x] **[SERVER]** Create environment configuration (.env setup)
- [x] **[SERVER]** Set up TypeScript configuration for server
- [x] **[SERVER]** Create basic server entry point (app.js/index.ts)

## Project Structure

- [ ] **[SERVER]** Create auth middleware for validating tokens from PY backend
- [ ] **[SERVER]** Implement auth API client to communicate with Python backend
- [ ] **[SERVER]** Set up token validation for Socket.IO connections
- [ ] **[SERVER]** Create user session management
- [ ] **[SERVER]** Implement secure room access control

### Server Infrastructure & Setup

- [x] **[SERVER]** Design room data structure using in-memory hash maps
- [x] **[SERVER]** Implement room creation and joining logic with hash maps
- [x] **[SERVER]** Create room state management (active players, game state) in memory
- [x] **[SERVER]** Implement basic room cleanup (manual/timeout-based)
- [ ] **[SERVER]** Add room capacity and validation logic

- [ ] **[SERVER]** Create environment configuration (.env setup)
- [ ] **[SERVER]** Set up TypeScript configuration for server
- [x] **[SERVER]** Design Socket.IO event structure
- [x] **[SERVER]** Implement player connection/disconnection handling
- [x] **[SERVER]** Create game state synchronization
- [x] **[SERVER]** Implement real-time score updates
- [x] **[SERVER]** Add game timer management
- [ ] **[SERVER]** Create opponent matching system
- [x] **[CLIENT]** Implement Socket.IO client integration
- [x] **[CLIENT]** Create postMessage communication handler for PWA iframe
- [x] **[CLIENT]** Implement postMessage listener for PWA communication

### Game Room Management

- [ ] **[SERVER]** Implement score submission to Python backend
- [ ] **[SERVER]** Create score validation and anti-cheat measures
- [ ] **[SERVER]** Set up leaderboard data management
- [ ] **[SERVER]** Implement score history tracking
- [ ] **[SERVER]** Add game result persistence
- [x] **[CLIENT]** Process submit score ID for score submission
- [ ] **[CLIENT]** Set up score submission queue with submit score ID
- [x] **[CLIENT]** Process opponent score data for display
- [x] **[CLIENT]** Implement opponent score tracking and display

- [ ] **[SERVER]** Design Socket.IO event structure
- [ ] **[SERVER]** Implement player connection/disconnection handling
- [x] **[CLIENT]** Set up GameApp routing structure with game ID routing
- [x] **[CLIENT]** Create shared UI components library
- [ ] **[CLIENT]** Implement audio system for games
- [ ] **[CLIENT]** Create utility functions and helpers
- [x] **[CLIENT]** Set up game state management (stores)
- [x] **[CLIENT]** Implement game ID routing to specific games
- [ ] **[CLIENT]** Implement postMessage listener for PWA communication

- [x] **[CLIENT]** Create game state store for received PWA data
- [x] **[CLIENT]** Implement game lifecycle management within iframe
- [ ] **[CLIENT]** Create error handling for invalid PWA data

- [ ] **[SERVER]** Create score validation and anti-cheat measures
- [ ] **[SERVER]** Set up leaderboard data management
- [x] **[CLIENT]** Integrate Finger Frenzy game with generic UI components
- [ ] **[CLIENT]** Integrate Bingo game with generic UI components
- [ ] **[CLIENT]** Integrate Matching Mayhem game with generic UI components
- [ ] **[CLIENT]** Integrate Number Sequence game with generic UI components
- [x] **[CLIENT]** Refactor games to use shared countdown timer
- [x] **[CLIENT]** Refactor games to use universal HUD component
- [x] **[CLIENT]** Refactor games to use generic end game screen
- [ ] **[CLIENT]** Implement game-specific scoring interfaces
- [ ] **[CLIENT]** Add Socket.IO integration to each game for real-time updates
- [x] **[CLIENT]** Set up GameApp routing structure with game ID routing
- [x] **[CLIENT]** Create shared UI components library
- [x] **[SERVER]** Implement Finger Frenzy timer and scoring logic
- [x] **[SERVER]** Implement Bingo timer and scoring logic
- [x] **[SERVER]** Implement Matching Mayhem timer and scoring logic
- [ ] **[SERVER]** Implement Number Sequence timer and scoring logic
- [ ] **[SERVER]** Create game-specific validation rules
- [ ] **[SERVER]** Implement anti-cheat measures for each game type
- [x] **[SERVER]** Add game duration management and timeouts
- [ ] **[SERVER]** Create game result calculation and comparison logic
- [ ] **[CLIENT]** Design universal HUD component (score, time, progress)
- [ ] **[CLIENT]** Implement generic end game screen component
- [ ] **[CLIENT]** Create game overlay system for UI integration
- [ ] **[CLIENT]** Design responsive UI components for different screen sizes

### PWA Integration & Authentication

- [ ] **[CLIENT]** Handle auth token reception and validation from PWA
- [ ] **[CLIENT]** Implement auth token storage and refresh logic
- [ ] **[CLIENT]** Create secure postMessage validation
- [ ] **[CLIENT]** Handle room ID for multiplayer game joining
- [ ] **[CLIENT]** Create room state management with received room ID

### Game State & Lifecycle Management

- [ ] **[CLIENT]** Create game state store for received PWA data
- [ ] **[CLIENT]** Implement game lifecycle management within iframe
- [ ] **[CLIENT]** Create error handling for invalid PWA data

### Phaser 3 Game Integration

- [x] **[CLIENT]** Integrate Finger Frenzy game with generic UI components
- [ ] **[CLIENT]** Integrate Bingo game with generic UI components
- [ ] **[CLIENT]** Integrate Matching Mayhem game with generic UI components
- [ ] **[CLIENT]** Integrate Number Sequence game with generic UI components
- [x] **[CLIENT]** Refactor games to use shared countdown timer
- [x] **[CLIENT]** Refactor games to use universal HUD component
- [x] **[CLIENT]** Refactor games to use generic end game screen
- [ ] **[CLIENT]** Implement game-specific scoring interfaces
- [ ] **[CLIENT]** Add Socket.IO integration to each game for real-time updates

## Per-Game Tasks

### Finger Frenzy

- [x] Client: integrate with generic UI components (countdown, HUD, end screen)
- [x] Server: timer & scoring logic (server-side game timers, updateScore, deductLife)
- [x] Server: action validation for tile_tap
- [x] Server+Client: Socket.IO integration (start, action, action_result, timer_tick)
- [ ] Server: submit final score to Python backend (submit_score endpoint)
- [ ] Client: finalize game-specific scoring interface / submit flow

### Bingo

- [ ] Client: integrate with generic UI components (countdown, HUD, end screen)
- [x] Server: PRNG-based card generation and number calling
- [x] Server: timing-based scoring and automatic callout timers
- [x] Server+Client: Socket.IO integration (number_called, cell_mark, action_result)
- [ ] Server: submit final score to Python backend
- [ ] Client: implement game-specific scoring interface and visual marks

### Matching Mayhem

- [ ] Client: integrate with generic UI components (countdown, HUD, end screen)
- [x] Server: round generation, round timers, scoring and penalties
- [x] Server+Client: Socket.IO integration (round_timer_tick, card_select, action_result)
- [ ] Server: submit final score to Python backend
- [ ] Client: implement game-specific scoring interface and next-round flow

### Number Sequence

- [ ] Client: integrate with generic UI components (countdown, HUD, end screen)
- [ ] Server: timer & scoring logic
- [ ] Server+Client: Socket.IO integration (start, action, action_result, timer_tick)
- [ ] Server: submit final score to Python backend
- [ ] Client: implement game-specific scoring interface

### Cross-game / Shared Work

- [ ] Server: score submission endpoint & integration with Python backend (common for all games)
- [ ] Server: leaderboard, score history and persistence
- [ ] Server: anti-cheat/validation rules per game (extendable list)
- [ ] Client: shared testing harness / e2e test for one multiplayer flow

### Server-Side Game Logic

- [ ] **[SERVER]** Implement Finger Frenzy timer and scoring logic
- [ ] **[SERVER]** Implement Bingo timer and scoring logic
- [ ] **[SERVER]** Implement Matching Mayhem timer and scoring logic
- [ ] **[SERVER]** Implement Number Sequence timer and scoring logic
- [ ] **[SERVER]** Create game-specific validation rules
- [ ] **[SERVER]** Implement anti-cheat measures for each game type
- [ ] **[SERVER]** Add game duration management and timeouts
- [ ] **[SERVER]** Create game result calculation and comparison logic

## Technical Specifications

### Server Technologies

- Node.js with Express.js
- Socket.IO for real-time communication
- Redis for session and game state management
- TypeScript for type safety
- JWT for authentication validation

### Client Technologies

- SvelteKit for the GameApp framework
- Phaser 3 for game engines (4 existing games)
- Socket.IO client for real-time communication
- TypeScript for type safety
- Tailwind CSS for styling

### Game Portfolio

- **Finger Frenzy**: Fast-paced finger tapping game
- **Bingo**: Classic bingo with multiplayer features
- **Matching Mayhem**: Memory/matching card game
- **Number Sequence**: Pattern recognition and sequence game

### Communication Protocols

- REST API for auth validation with Python backend
- Socket.IO for real-time game communication
- PostMessage API for iframe communication with PWA

### PWA to GameApp Data Flow (via postMessage)

- **Auth Token**: JWT token for user authentication
- **Submit Score ID**: Unique identifier for score submission tracking
- **Room ID**: Multiplayer room identifier (if joining existing room)
- **Opponent Score**: Current opponent's score (if applicable)
- **Game ID**: Identifier for routing to specific game component

## Next Immediate Steps

### Server Setup Priority

1. Create server directory structure
2. Initialize Node.js project with dependencies
3. Set up basic Express server with Socket.IO
4. Set up in-memory data structures (hash maps for rooms and sessions)
5. Create initial auth middleware
6. Implement basic room management with hash maps

### Client Integration Priority

1. Create generic UI components (countdown, HUD, end game)
2. Set up game routing for the 4 Phaser 3 games
3. Integrate first game (Finger Frenzy) with generic UI
4. Implement server-side timer and scoring for Finger Frenzy
5. Test end-to-end multiplayer functionality with one game
6. Replicate integration pattern for remaining 3 games

## Backlog (Future Enhancements)

### Redis Integration (For Production/Persistence)

- [ ] **[SERVER]** Set up Redis connection and configuration
- [ ] **[SERVER]** Migrate room data structure from hash maps to Redis

## Notes

- **Current Approach**: Using in-memory hash maps for quick development and testing
- **Future Migration**: Redis integration planned for production persistence and scaling
- Ensure all communications are secure and validated
- Implement proper error handling and logging
- Consider scalability for multiple concurrent games
- Plan for graceful degradation if services are unavailable
