import * as Phaser from 'phaser';
import TicTapsConnector from '../utils/TicTapsConnector';
import type { GameEndSceneData } from '../types/types';

export default class GameEndScene extends Phaser.Scene {
  private ticTaps!: TicTapsConnector;
  private score: number = 0;
  private backToLobbyButton!: Phaser.GameObjects.Container;

  constructor() {
    super('GameEndScene');
  }

  init(data: GameEndSceneData): void {
    this.score = data.score || 0;
  }

  create(): void {
    // Initialize TicTaps connector
    this.ticTaps = new TicTapsConnector();
    
    // Use the same background as GameStartScene
    this.add.image(0, 0, 'game_background')
      .setOrigin(0, 0)
      .setDisplaySize(this.cameras.main.width, this.cameras.main.height);
    
    // Create the main rounded rectangle panel
    const panelWidth = this.cameras.main.width * 0.8;
    const panelHeight = this.cameras.main.height * 0.6; // Taller panel like in Image 2
    
    // Semi-transparent panel with blur effect
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      // First create a slightly larger background for the blur effect
      const blurBg = this.add.graphics();
      blurBg.fillStyle(0x000000, 0.3);
      blurBg.fillRoundedRect(
        this.cameras.main.width / 2 - panelWidth / 2 - 2,
        this.cameras.main.height / 2 - panelHeight / 2 - 2,
        panelWidth + 4,
        panelHeight + 4,
        20
      );
      
      // Add blur effect
      blurBg.postFX.addBlur(0, 0, 1, 2, 1, 1);
    }
    
    // Main panel background - dark with transparency like in Image 2
    const panel = this.add.graphics();
    panel.fillStyle(0x1a2331, 0.4); // Semi-transparent dark color
    panel.fillRoundedRect(
      this.cameras.main.width / 2 - panelWidth / 2,
      this.cameras.main.height / 2 - panelHeight / 2,
      panelWidth,
      panelHeight,
      20
    );
    
    // Add the GAME OVER image at the top of the panel
    const gameOverImage = this.add.image(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - panelHeight * 0.5, // Positioned at top of panel
      'game_over'
    ).setOrigin(0.5);
    
    // Scale the image appropriately to match Image 2
    const gameOverScale = (panelWidth * 0.8) / gameOverImage.width;
    gameOverImage.setScale(gameOverScale);
    
    // Add glow effect to GAME OVER image - brighter glow like in Image 2
    if (this.sys.game.renderer.type === Phaser.WEBGL) {
      gameOverImage.postFX.addGlow(0x4bffae, 1.0, 0, false, 0.1, 15);
    }

    // Add "SCORE" text in white - positioned in the center of the panel
    this.add.text(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2 - 100, // Slight offset above center
      'SCORE',
      {
        fontFamily: 'Arial',
        fontSize: '30px',
        fontStyle: 'bold',
        color: '#FFFFFF'
      }
    ).setOrigin(0.5);

    // Create score value with gradient matching Image 2
    this.createGradientText(
      this.cameras.main.width / 2,
      this.cameras.main.height / 2, // Below "SCORE" text
      this.score.toString(),
      90,
      true // Make it with gradient and white outline
    );
    
    // Create the BACK TO LOBBY button with gradient border
    this.createBackToLobbyButton();
  }
  
  /**
   * Create the Back to Lobby button with gradient border as in Image 2
   */
  private createBackToLobbyButton(): void {
    const buttonWidth = this.cameras.main.width * 0.7;
    const buttonHeight = 80;
    const buttonX = this.cameras.main.width / 2;
    const buttonY = this.cameras.main.height / 2 + this.cameras.main.height * 0.20; // Position near bottom of panel
    
    // Create a container for all button elements
    this.backToLobbyButton = this.add.container(buttonX, buttonY);
    
    // Create the gradient border canvas for a more exact match to Image 2
    const borderCanvas = this.textures.createCanvas('buttonBorder', buttonWidth + 4, buttonHeight + 4);
    if (borderCanvas) {
      const borderContext = borderCanvas.getContext();
      
      // Create a gradient from left to right - match Image 2's colors exactly
      const gradient = borderContext.createLinearGradient(0, 0, buttonWidth + 4, 0);
      gradient.addColorStop(0, '#32c4ff');  // Cyan on left
      gradient.addColorStop(0.5, '#7f54ff'); // Purple in middle
      gradient.addColorStop(1, '#b63efc');   // Pink on right
      
      // Draw rounded rectangle with gradient
      borderContext.strokeStyle = gradient;
      borderContext.lineWidth = 2.5;
      roundRect(borderContext, 2, 2, buttonWidth, buttonHeight, 18, false, true);
      
      borderCanvas.refresh();
      
      // Add the border image
      const border = this.add.image(0, 0, 'buttonBorder').setOrigin(0.5);
      this.backToLobbyButton.add(border);
    }
    
    // Create the button background - darker to match Image 2
    const buttonBg = this.add.graphics();
    buttonBg.fillStyle(0x12161f, 1); // Very dark background like in Image 2
    buttonBg.fillRoundedRect(-buttonWidth/2 + 2, -buttonHeight/2 + 2, buttonWidth - 4, buttonHeight - 4, 16);
    this.backToLobbyButton.add(buttonBg);
    
    // Use the "back_to_lobby.png" image if available, or create gradient text
    if (this.textures.exists('back_to_lobby')) {
      const buttonText = this.add.image(0, 0, 'back_to_lobby').setOrigin(0.5);
      // Scale to fit inside the button appropriately
      const textScale = Math.min((buttonWidth * 0.7) / buttonText.width, (buttonHeight * 0.6) / buttonText.height);
      buttonText.setScale(textScale);
      this.backToLobbyButton.add(buttonText);
    } else {
      // Create gradient text if the image isn't available - italicized like in Image 2
      const buttonText = this.createGradientText(0, 0, 'BACK TO LOBBY', 28, false, true);
      this.backToLobbyButton.add(buttonText);
    }
    
    // Make container interactive
    const hitArea = new Phaser.Geom.Rectangle(-buttonWidth/2, -buttonHeight/2, buttonWidth, buttonHeight);
    this.backToLobbyButton.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);
    
    // Add hover effect
    this.backToLobbyButton.on('pointerover', () => {
      this.backToLobbyButton.setScale(1.05);
    });
    
    this.backToLobbyButton.on('pointerout', () => {
      this.backToLobbyButton.setScale(1);
    });
    
    // Add click handler
    this.backToLobbyButton.on('pointerdown', () => {
      // Play sound if available
      if (this.sound.get('laser')) {
        this.sound.play('laser', { volume: 0.7 });
      } else if (this.sound.get('countdown')) {
        this.sound.play('countdown', { volume: 0.7 });
      }
      
      // Scale down slightly on press
      this.backToLobbyButton.setScale(0.95);
      
      this.time.delayedCall(100, () => {
        this.backToLobbyButton.setScale(1);
        this.endGame();
      });
    });
  }
  
  /**
   * Helper function to create gradient text
   */
  private createGradientText(x: number, y: number, text: string, fontSize: number = 32, isScoreText: boolean = false, isButtonText: boolean = false): Phaser.GameObjects.Image {
    // Generate a unique texture key based on the text and size
    const textureName = 'gradientText-' + text.replace(/\s+/g, '-') + '-' + fontSize + (isScoreText ? '-score' : '') + (isButtonText ? '-button' : '');
    
    // Remove any previous version of this texture if it exists
    if (this.textures.exists(textureName)) {
      this.textures.remove(textureName);
    }
    
    // Calculate canvas size based on text length
    const width = Math.max(400, text.length * fontSize * 0.7);
    const height = fontSize * 1.5;
    
    // Create a canvas texture
    const textCanvas = this.textures.createCanvas(textureName, width, height);
    if (!textCanvas) {
      console.error('Failed to create gradient text canvas');
      return this.add.image(x, y, '').setOrigin(0.5);
    }
    
    const context = textCanvas.getContext();
    
    // Create a gradient - match the exact colors from Image 2
    const gradient = context.createLinearGradient(0, 0, width, height * 0.5);
    
    if (isScoreText) {
      // Exact gradient from Image 2 (green to blue)
      gradient.addColorStop(0, '#4cffae');  // Bright green
      gradient.addColorStop(0.4, '#32c4ff'); // Cyan
      gradient.addColorStop(1, '#5c67ff');   // Blue
    } else if (isButtonText) {
      // Gradient for BACK TO LOBBY button text from Image 2
      gradient.addColorStop(0, '#32c4ff');  // Cyan on left
      gradient.addColorStop(0.5, '#7f54ff'); // Purple in middle
      gradient.addColorStop(1, '#b63efc');   // Pink on right
    } else {
      // Standard cyan-to-purple gradient for other text
      gradient.addColorStop(0, '#33DDFF'); // Light blue/cyan
      gradient.addColorStop(1, '#664DFF'); // Purple
    }
    
    // Set text properties
    context.font = `bold ${fontSize}px Arial`;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    
    // For score text, add white outline like in Image 2
    if (isScoreText) {
      // First create a white outline/glow
      context.strokeStyle = 'rgba(255, 255, 255, 0.9)';
      context.lineWidth = 5;
      context.strokeText(text, width / 2, height / 2);
    }
    
    // Fill with gradient
    context.fillStyle = gradient;
    context.fillText(text, width / 2, height / 2);
    
    // Update the texture
    textCanvas.refresh();
    
    // Create and return an image with the texture
    return this.add.image(x, y, textureName).setOrigin(0.5);
  }
  
  private endGame(): void {
    // Create a white flash effect
    const flash = this.add.rectangle(
      this.cameras.main.width / 2, 
      this.cameras.main.height / 2,
      this.cameras.main.width,
      this.cameras.main.height,
      0xffffff
    ).setAlpha(0).setOrigin(0.5);
    
    // Make sure flash is on top of everything
    flash.setDepth(1000);
    
    // Send score to TicTaps before quitting
    this.ticTaps.sendScore(this.score);
    
    // Notify TicTaps that the player is quitting - this is the critical call
    this.ticTaps.notifyGameQuit();
    
    // Flash animation
    this.tweens.add({
      targets: flash,
      alpha: 0.8,
      duration: 100,
      ease: 'Sine.easeOut',
      onComplete: () => {
        // Hold briefly then fade out
        this.tweens.add({
          targets: flash,
          alpha: 0,
          delay: 50,
          duration: 250,
          ease: 'Sine.easeIn',
          onComplete: () => {
            // Restart the game
            this.scene.start('GameStartScene');
          }
        });
      }
    });
  }
}

// Helper function for drawing rounded rectangles
function roundRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number, fill: boolean, stroke: boolean) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  if (fill) {
    ctx.fill();
  }
  if (stroke) {
    ctx.stroke();
  }
}