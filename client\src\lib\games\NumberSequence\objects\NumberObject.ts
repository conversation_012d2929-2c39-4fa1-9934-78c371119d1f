import * as Phaser from 'phaser';
import GameConfig from '../config/GameConfig';
import type { NumberObjectState } from '../types/types';

export default class NumberObject extends Phaser.GameObjects.Container {
  private number: number;
  private circle: Phaser.GameObjects.Sprite;
  private text: Phaser.GameObjects.Text;
  private glowEffect: Phaser.GameObjects.Graphics;
  private innerStroke!: Phaser.GameObjects.Graphics;
  private originalX: number;
  private originalY: number;
  private radius: number;
  private objectState: NumberObjectState = 'active';

  constructor(scene: Phaser.Scene, x: number, y: number, number: number) {
    super(scene, x, y);

    this.number = number;
    this.originalX = x;
    this.originalY = y;
    this.radius = GameConfig.CIRCLE_RADIUS; // 35 * 1.1 = 38.5 (10% larger)

    // Create glow effect (behind the circle)
    this.glowEffect = scene.add.graphics();
    this.createGlowEffect();
    this.add(this.glowEffect);

    // Create the circle using PNG image
    this.circle = scene.add.sprite(0, 0, 'circle');
    this.circle.setScale(this.radius * 2 / 160); // Scale to match the radius (PNG is 160x160)
    
    // Make the circle itself interactive with a larger hit area
    // This makes the entire sprite area clickable
    this.circle.setInteractive();
    this.circle.on('pointerdown', () => {
    this.onPointerDown();
    });
    
    this.add(this.circle);

    // Create white inner stroke on top of the circle
    this.createInnerStroke();

    // Create number text
    this.text = scene.add.text(0, 0, (number + 1).toString(), {
      fontSize: '46px',  // Increased proportionally (42 * 1.1 = 46.2)
      fontFamily: 'Arial',
      color: '#ffffff',
      stroke: '#003366',
      strokeThickness: 2
    });
    this.text.setOrigin(0.5);
    this.add(this.text);

    // Add to the scene
    this.scene.add.existing(this);

    // Make the container interactive with a circular hit area
    // This provides a backup hitbox in case the sprite's hitbox has issues
    this.setSize(this.radius * 2, this.radius * 2);
    this.setInteractive(new Phaser.Geom.Circle(0, 0, this.radius), Phaser.Geom.Circle.Contains);
    this.on('pointerdown', () => {
      this.onPointerDown();
    });

    // Start spawn animation
    this.playSpawnAnimation();
  }

  public get numberValue(): number {
    return this.number;
  }

  public get currentState(): NumberObjectState {
    return this.objectState;
  }

  public setObjectState(newState: NumberObjectState): void {
    this.objectState = newState;
  }

  // No need for createCircle method as we're using an SVG image

  private createGlowEffect(): void {
    this.glowEffect.clear();

    // Create multiple rings for the glow effect
    for (let i = this.radius + 15; i >= this.radius; i -= 2) {
      const opacity = (this.radius + 16 - i) / 16;
      this.glowEffect.lineStyle(3, 0x00bbff, opacity * 0.6);
      this.glowEffect.strokeCircle(0, 0, i);
    }

    // Add inner subtle glow
    this.glowEffect.fillStyle(0x003366, 0.4);
    this.glowEffect.fillCircle(0, 0, this.radius);
  }

  public setActive(active: boolean): this {
    if (active) {
      // When active, show brighter glow
      this.glowEffect.clear();
      this.glowEffect.fillStyle(0x0088ff, 0.9);
      this.glowEffect.fillCircle(0, 0, this.radius);
      this.glowEffect.lineStyle(4, 0x00ffff, 1);
      this.glowEffect.strokeCircle(0, 0, this.radius);

      // Make the circle brighter
      this.circle.setAlpha(1);
      this.setObjectState('active');
    } else {
      this.createGlowEffect();
      this.circle.setAlpha(0.9);
      this.setObjectState('inactive');
    }
    return this;
  }

  private createInnerStroke(): void {
    this.innerStroke = this.scene.add.graphics();
    this.innerStroke.lineStyle(2, 0xffffff, 0.8); // White stroke with slight transparency
    this.innerStroke.strokeCircle(0, 0, this.radius - 3); // Slightly inside the edge
    this.add(this.innerStroke);
  }

  public setError(): void {
    this.setObjectState('error');

    // Show red tint for error
    this.glowEffect.clear();
    this.glowEffect.fillStyle(0xff0000, 0.7);
    this.glowEffect.fillCircle(0, 0, this.radius);
    this.glowEffect.lineStyle(4, 0xff0000, 1);
    this.glowEffect.strokeCircle(0, 0, this.radius);

    // Apply red tint to the circle
    this.circle.setTint(0xff6666);

    // Shake animation
    this.scene.tweens.add({
      targets: this,
      x: this.x + 3,
      duration: 50, //MORE TIME PUNISHED FOR WRONG SELECTION
      yoyo: true,
      repeat: 5,
      ease: 'Power1',
      onComplete: () => {
        this.x = this.originalX;
        this.y = this.originalY;
        this.clearError();
      }
    });
  }

  public clearError(): void {
    this.createGlowEffect();
    // Clear the red tint from the circle
    this.circle.clearTint();
    this.setObjectState('active');
  }

  public reset(): void {
    this.setActive(false);
    this.clearError();
    this.setVisible(true);
    this.alpha = 1;
  }

  private onPointerDown(): void {
    const gameScene = this.scene as any;
    if (gameScene.handleNumberClick) {
      gameScene.handleNumberClick(this);
    }
  }

  public startRotation(): void {
    this.scene.tweens.add({
      targets: this,
      angle: -360,
      duration: 2000,
      repeat: -1,
      ease: 'Linear'
    });
  }

  public stopRotation(): void {
    this.scene.tweens.killTweensOf(this);
    this.angle = 0;
  }

  private playSpawnAnimation(): void {
    // Start at 0% scale
    this.setScale(0);
    
    // Animate from 0% to 110% then back to 100% in 500ms
    this.scene.tweens.add({
      targets: this,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 250, // Half the total duration to reach 110%
      ease: 'Back.easeOut',
      onComplete: () => {
        // Second part: scale back down to 100%
        this.scene.tweens.add({
          targets: this,
          scaleX: 1.0,
          scaleY: 1.0,
          duration: 250, // Remaining duration to reach 100%
          ease: 'Back.easeIn'
        });
      }
    });
  }

  public playDisappearAnimation(onComplete: () => void): void {
    // Animate scale down to 0% then make invisible
    this.scene.tweens.add({
      targets: this,
      scaleX: 0,
      scaleY: 0,
      duration: 300, // Quick disappear animation
      ease: 'Back.easeIn',
      onComplete: () => {
        this.setVisible(false);
        onComplete(); // Call the callback when animation is complete
      }
    });
  }
}
