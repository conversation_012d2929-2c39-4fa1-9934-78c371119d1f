import { AuthenticatedUser, UserSession, RoomSession } from '../types/game.js';
import { logger } from '../utils/logger.js';

/**
 * Session Service for managing authenticated user sessions and room data
 * Integrates with existing game session storage
 */
export class SessionService {
  private userSessions: Map<string, UserSession> = new Map();
  private roomSessions: Map<string, RoomSession> = new Map();
  private socketToUser: Map<string, string> = new Map();

  /**
   * Create or update a user session
   * @param user - Authenticated user data
   * @param socketId - Socket ID
   * @returns Created user session
   */
  public createUserSession(user: AuthenticatedUser, socketId: string): UserSession {
    const now = new Date();
    
    const session: UserSession = {
      user,
      socketId,
      connectedAt: now,
      lastActivity: now
    };

    // Store user session
    this.userSessions.set(user.userId, session);
    this.socketToUser.set(socketId, user.userId);

    // Create or update room session
    this.addUserToRoom(user, session);

    logger.info('User session created', {
      userId: user.userId,
      socketId,
      roomId: user.roomId,
      gameId: user.gameId
    });

    return session;
  }

  /**
   * Get user session by user ID
   * @param userId - User ID
   * @returns User session or null
   */
  public getUserSession(userId: string): UserSession | null {
    return this.userSessions.get(userId) || null;
  }

  /**
   * Get user session by socket ID
   * @param socketId - Socket ID
   * @returns User session or null
   */
  public getUserSessionBySocket(socketId: string): UserSession | null {
    const userId = this.socketToUser.get(socketId);
    return userId ? this.getUserSession(userId) : null;
  }

  /**
   * Update user activity timestamp
   * @param userId - User ID
   */
  public updateUserActivity(userId: string): void {
    const session = this.userSessions.get(userId);
    if (session) {
      session.lastActivity = new Date();
      
      // Also update room activity
      const roomSession = this.roomSessions.get(session.user.roomId);
      if (roomSession) {
        roomSession.lastActivity = new Date();
      }
    }
  }

  /**
   * Remove user session
   * @param userId - User ID
   */
  public removeUserSession(userId: string): void {
    const session = this.userSessions.get(userId);
    if (session) {
      // Remove from socket mapping
      this.socketToUser.delete(session.socketId);
      
      // Remove from room
      this.removeUserFromRoom(session.user.roomId, userId);
      
      // Remove user session
      this.userSessions.delete(userId);

      logger.info('User session removed', {
        userId,
        socketId: session.socketId,
        roomId: session.user.roomId
      });
    }
  }

  /**
   * Remove user session by socket ID
   * @param socketId - Socket ID
   */
  public removeUserSessionBySocket(socketId: string): void {
    const userId = this.socketToUser.get(socketId);
    if (userId) {
      this.removeUserSession(userId);
    }
  }

  /**
   * Get room session
   * @param roomId - Room ID
   * @returns Room session or null
   */
  public getRoomSession(roomId: string): RoomSession | null {
    return this.roomSessions.get(roomId) || null;
  }

  /**
   * Get all users in a room
   * @param roomId - Room ID
   * @returns Array of user sessions in the room
   */
  public getRoomUsers(roomId: string): UserSession[] {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? Array.from(roomSession.users.values()) : [];
  }

  /**
   * Get user count in a room
   * @param roomId - Room ID
   * @returns Number of users in the room
   */
  public getRoomUserCount(roomId: string): number {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? roomSession.users.size : 0;
  }

  /**
   * Check if user is in a specific room
   * @param userId - User ID
   * @param roomId - Room ID
   * @returns true if user is in the room
   */
  public isUserInRoom(userId: string, roomId: string): boolean {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? roomSession.users.has(userId) : false;
  }

  /**
   * Get session data for game controllers (compatible with existing structure)
   * @param roomId - Room ID
   * @returns Session data object
   */
  public getGameSessionData(roomId: string): { submitScoreId?: string; authToken?: string } | null {
    const roomSession = this.roomSessions.get(roomId);
    if (!roomSession || roomSession.users.size === 0) {
      return null;
    }

    // Get the first user's data (for single-player games)
    const firstUser = Array.from(roomSession.users.values())[0];
    return {
      submitScoreId: firstUser.user.scoreSubmitId,
      authToken: firstUser.user.authToken
    };
  }

  /**
   * Clean up expired sessions
   * @param maxIdleTime - Maximum idle time in milliseconds (default: 1 hour)
   */
  public cleanupExpiredSessions(maxIdleTime: number = 60 * 60 * 1000): void {
    const now = new Date();
    const expiredUsers: string[] = [];

    for (const [userId, session] of this.userSessions) {
      if (now.getTime() - session.lastActivity.getTime() > maxIdleTime) {
        expiredUsers.push(userId);
      }
    }

    for (const userId of expiredUsers) {
      this.removeUserSession(userId);
    }

    if (expiredUsers.length > 0) {
      logger.info('Cleaned up expired sessions', { count: expiredUsers.length });
    }
  }

  /**
   * Add user to room session
   * @param user - Authenticated user
   * @param session - User session
   */
  private addUserToRoom(user: AuthenticatedUser, session: UserSession): void {
    let roomSession = this.roomSessions.get(user.roomId);
    
    if (!roomSession) {
      roomSession = {
        roomId: user.roomId,
        gameId: user.gameId,
        users: new Map(),
        createdAt: new Date(),
        lastActivity: new Date()
      };
      this.roomSessions.set(user.roomId, roomSession);
      
      logger.info('Room session created', {
        roomId: user.roomId,
        gameId: user.gameId
      });
    }

    roomSession.users.set(user.userId, session);
    roomSession.lastActivity = new Date();
  }

  /**
   * Remove user from room session
   * @param roomId - Room ID
   * @param userId - User ID
   */
  private removeUserFromRoom(roomId: string, userId: string): void {
    const roomSession = this.roomSessions.get(roomId);
    if (roomSession) {
      roomSession.users.delete(userId);
      
      // Remove room if empty
      if (roomSession.users.size === 0) {
        this.roomSessions.delete(roomId);
        logger.info('Room session removed (empty)', { roomId });
      }
    }
  }

  /**
   * Get all active sessions (for debugging/monitoring)
   */
  public getStats() {
    return {
      userSessions: this.userSessions.size,
      roomSessions: this.roomSessions.size,
      socketMappings: this.socketToUser.size
    };
  }
}

// Singleton instance
export const sessionService = new SessionService();
