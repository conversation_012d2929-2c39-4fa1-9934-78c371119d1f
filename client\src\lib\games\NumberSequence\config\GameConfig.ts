/**
 * GameConfig - Centralized configuration for game settings
 * This file contains all configurable parameters for the game
 */
export default class GameConfig {
  static readonly GAME_DURATION: number = 30; // in seconds
  static readonly SOUND_VOLUME: number = 0.7;
  static readonly CIRCLE_RADIUS: number = 38.5; // 35 * 1.1 = 38.5 (10% larger)

  // Animation settings
  static readonly FLASH_DURATION: number = 100; // milliseconds
  static readonly TRANSITION_DELAY: number = 50; // milliseconds
  static readonly TRANSITION_FADE_DURATION: number = 250; // milliseconds
}
