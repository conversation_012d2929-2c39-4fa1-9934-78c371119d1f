import * as Phaser from 'phaser';
import type GameScene from '../scenes/GameScene';
import type { BingoColumn } from '../types/BingoTypes';

/**
 * RightNumber - Number item displayed on the right panel
 * Equivalent to RightNumberController in Unity
 */
export default class RightNumber extends Phaser.GameObjects.Container {
  public rightIndex: number;
  public letter: BingoColumn;
  public number: number;
  public timerValue: number;
  private timerTween: Phaser.Tweens.Tween | null;
  private blinkTimer: Phaser.Time.TimerEvent | null;
  private timerFill!: Phaser.GameObjects.Graphics;
  private numberText!: Phaser.GameObjects.Text;
  private letterText!: Phaser.GameObjects.Text;
  private gameScene: GameScene; // Store the GameScene reference

  constructor(scene: GameScene, x: number, y: number, index: number, letter: BingoColumn, number: number) {
    super(scene, x, y);

    // Store properties with proper GameScene type
    this.scene = scene;
    this.gameScene = scene; // Store direct reference
    this.rightIndex = index;
    this.letter = letter;
    this.number = number;
    this.name = `${letter}${number}`;
    this.timerValue = 1;
    this.timerTween = null;
    this.blinkTimer = null;

    // Create visual components
    this.createVisuals();

    // Start timer
    this.startTimer();

    // Add to scene
    scene.add.existing(this);

    // Initial scale animation with a bounce effect
    this.setScale(0);
    this.scene.tweens.add({
      targets: this,
      scale: 1,
      duration: 400,
      ease: 'Back.Out',
      delay: 100
    });
  }

  createVisuals() {
    const cellSize = 85;

    // Create circular background using graphics
    const bgGraphics = this.scene.add.graphics();

    // Use gradient for the circle
    bgGraphics.fillGradientStyle(
        0x3066FF, // Top-left: blue
        0x4752FF, // Top-right: blue-purple
        0x5E4DFF, // Bottom-right: more purple
        0x215EFF, // Bottom-left: blue
        1
    );

    // Draw the filled circle
    bgGraphics.fillCircle(0, 0, cellSize/2);

    // Add border separately
    bgGraphics.lineStyle(3, 0x00E5AE, 1);

    this.add(bgGraphics);

    // The rest of your method for timer, number text, etc.
    // Create timer fill using graphics object
    this.timerFill = this.scene.add.graphics();
    this.add(this.timerFill);

    // Number text with drop shadow to match Unity
    this.numberText = this.scene.add.text(
        0,
        5, // Slight offset for better visual balance
        this.number.toString(),
        {
          fontFamily: '"TT Neoris", Arial, sans-serif',
          fontSize: '38px',
          color: '#FFFFFF',
          align: 'center',
          fontStyle: 'bold',
          stroke: '#000000',
          strokeThickness: 2,
          shadow: { offsetX: 1, offsetY: 1, color: '#000000', blur: 2, fill: true }
        }
    ).setOrigin(0.5);
    this.add(this.numberText);

    // Letter label at top of circle
    this.letterText = this.scene.add.text(
        0,
        -22,
        this.letter,
        {
          fontFamily: '"TT Neoris", Arial, sans-serif',
          fontSize: '22px',
          color: '#FFFFFF',
          align: 'center',
          fontStyle: 'bold',
          stroke: '#000000',
          strokeThickness: 1
        }
    ).setOrigin(0.5);
    this.add(this.letterText);

    // Initial timer fill draw
    this.updateTimerVisual();
  }

  /**
   * Start countdown timer
   * Equivalent to timer() in Unity's RightNumberController
   */
  startTimer() {
    // Reset timer value
    this.timerValue = 1;

    // Update timer visually
    this.updateTimerVisual();

    // Create timer tween
    this.timerTween = this.scene.tweens.add({
      targets: this,
      timerValue: 0,
      duration: 8500,
      onUpdate: () => {
        this.updateTimerVisual();
      },
      onComplete: () => {
        // Start blinking matching bingo cell - using stored gameScene reference
        this.startBlinking();
      }
    });
  }

  /**
   * Update timer visual based on current timer value - enhanced to match Unity
   */
  updateTimerVisual() {
    // Clear previous graphics
    this.timerFill.clear();

    // Don't draw if timer is complete
    if (this.timerValue <= 0) return;

    // Draw the outer circle - white semi-transparent (slightly larger to match Unity)
    this.timerFill.lineStyle(4, 0xFFFFFF, 0.3);
    this.timerFill.strokeCircle(0, 0, 42); // Slightly smaller timer circle

    // Calculate angles for arc (clockwise from top)
    const startAngle = -90 * (Math.PI / 180); // -90 degrees in radians (starting from top)
    const endAngle = startAngle + (360 * this.timerValue) * (Math.PI / 180);

    // Draw the timer arc - aqua color to match Unity's highlighting
    this.timerFill.lineStyle(4, 0x00E5AE, 1);
    this.timerFill.beginPath();
    this.timerFill.arc(0, 0, 42, startAngle, endAngle, false);
    this.timerFill.strokePath();
  }

  /**
   * Calculate score based on timer value (DEPRECATED - server handles scoring)
   * This method is kept for backward compatibility with local fallback mode only
   * In production, all scoring is handled by the server based on timing
   */
  public getScore(): number {
    // NOTE: This is only used in local fallback mode
    // Server calculates timing-based scores using the same logic
    if (this.timerValue >= 1) return 100;
    return Math.max(10, 100 - Math.floor((1 - this.timerValue) * 100)); // minimum score of 10
  }

  /**
   * Start blinking matching bingo cell
   * Equivalent to startBlinking() in Unity's RightNumberController
   */
  startBlinking() {
    // Check if game scene is still valid and game hasn't ended
    if (!this.gameScene || this.gameScene.gameEnd) return;

    try {
      // Find matching bingo cell using the stored gameScene reference
      const matchingCell = this.gameScene.findBingoCellByName(this.name);

      if (matchingCell && !matchingCell.marked) {
        // Blink the matching cell
        matchingCell.blink();

        // Set timer to continue blinking every second
        this.blinkTimer = this.scene.time.addEvent({
          delay: 1000,
          callback: () => {
            try {
              // Check if the matching cell still exists and is not marked
              if (!this.gameScene || this.gameScene.gameEnd) {
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
                return;
              }

              const cell = this.gameScene.findBingoCellByName(this.name);
              if (cell && !cell.marked) {
                cell.blink();
              } else {
                // Stop blinking if cell is marked or doesn't exist
                if (this.blinkTimer) {
                  this.blinkTimer.remove();
                  this.blinkTimer = null;
                }
              }
            } catch (error) {
              // Handle any errors that might occur during callback execution
              console.log('Error in blink timer callback:', error);
              if (this.blinkTimer) {
                this.blinkTimer.remove();
                this.blinkTimer = null;
              }
            }
          },
          loop: true
        });
      }
    } catch (error) {
      // Handle any errors that might occur during startBlinking
      console.log('Error in startBlinking:', error);
    }
  }

  /**
   * Move this number down to a new position index
   * @param {number} newIndex - The new position index
   */
  public moveToPosition(newIndex: number): void {
    // Store previous index to detect when the item reaches center position
    const oldIndex = this.rightIndex;

    // Update index
    this.rightIndex = newIndex;

    // If the index is out of bounds, destroy this number
    if (this.rightIndex < 0) {
      // Destroy with animation
      this.scene.tweens.add({
        targets: this,
        scale: 0,
        duration: 300,
        ease: 'Back.In',
        onComplete: () => {
          this.destroy();
        }
      });
      return;
    }

    // Check if gameScene is valid before accessing its methods
    if (!this.gameScene) {
      this.destroy();
      return;
    }

    // Get new position
    const position = this.gameScene.getRightPosition(this.rightIndex);

    // Animate movement to new position with a slight bounce
    this.scene.tweens.add({
      targets: this,
      x: position.x,
      duration: 400,
      ease: 'Back.Out',
      easeParams: [1.5],
      onComplete: () => {
        // Check if this number is now in the center position (index 2)
        if (this.rightIndex === 2 && oldIndex !== 2) {
          // Find and flash the corresponding bingo cell to help the user
          if (!this.gameScene) return;

          const matchingCell = this.gameScene.findBingoCellByName(this.name);
          if (matchingCell && !matchingCell.marked) {
            // Use a more noticeable flash effect
            this.highlightMatchingCell(matchingCell);
          }
        }
      }
    });
  }

  /**
   * Create a highlight effect for the center position helper
   * @param {any} cell - The cell to highlight
   */
  private highlightMatchingCell(cell: any): void {
    try {
      // Create a more noticeable flash effect that's different from the timer expiration blink
      // First, make sure the cell exists and isn't already marked
      if (!this.gameScene || !cell || cell.marked || this.gameScene.gameEnd) return;

      // Create a temporary bright outline around the cell
      const highlight = this.scene.add.graphics();
      highlight.lineStyle(5, 0xFFFF00, 1); // Bright yellow outline
      highlight.strokeRoundedRect(
          cell.x - 40,
          cell.y - 40,
          80,
          80,
          16
      );

      // Create a pulsing animation
      this.scene.tweens.add({
        targets: highlight,
        alpha: { from: 1, to: 0 },
        duration: 800,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          try {
            highlight.destroy();
          } catch (error) {
            console.log('Error destroying highlight:', error);
          }
        }
      });

      // Also have the cell blink once
      try {
        cell.blink();
      } catch (error) {
        console.log('Error blinking cell:', error);
      }
    } catch (error) {
      console.log('Error in highlightMatchingCell:', error);
    }
  }

  /**
   * Destroy this number properly, stopping all timers
   */
  destroy(): void {
    try {
      // Stop timer tween
      if (this.timerTween) {
        this.timerTween.stop();
        this.timerTween = null;
      }

      // Stop blink timer
      if (this.blinkTimer) {
        this.blinkTimer.remove();
        this.blinkTimer = null;
      }

      // Clear references to prevent memory leaks
      this.gameScene = null as any;

      // Call parent destroy method
      super.destroy();
    } catch (error) {
      console.log('Error in RightNumber destroy:', error);
      // Ensure parent destroy is called even if there's an error
      try {
        super.destroy();
      } catch (e) {
        console.log('Error in super.destroy():', e);
      }
    }
  }
}
