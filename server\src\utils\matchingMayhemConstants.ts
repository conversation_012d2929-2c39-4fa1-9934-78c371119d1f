/**
 * Matching Mayhem Game Constants
 * Configuration constants for the Matching Mayhem game
 */

export const MATCHING_MAYHEM_CONFIG = {
  // Game timing
  GAME_DURATION: 20000, // 20 seconds in milliseconds
  ROUND_TIME: 5000, // 5 seconds per round in milliseconds
  
  // Card configuration
  CARD_COUNT: 5, // 5 cards per round
  CENTER_CARD_INDEX: 2, // Center card is always the matching distractor
  
  // Animal and color configuration
  ANIMAL_COUNT: 4, // 4 different animals
  COLOR_COUNT: 3, // 3 different colors
  
  // Scoring system
  SCORING: {
    MAX_ROUND_SCORE: 100, // Maximum points for immediate selection
    MIN_ROUND_SCORE: 10,  // Minimum points regardless of timing
    WRONG_ANSWER_PENALTY: 20, // Points deducted for wrong answer
    TIME_BONUS_MULTIPLIER: 1.0 // Multiplier for time-based bonus
  },
  
  // Lives system
  LIVES: {
    INITIAL_LIVES: 3,
    DEDUCT_ON_WRONG: 1
  },
  
  // Timing thresholds for scoring
  SCORE_TIMING: {
    EXCELLENT: 1000, // < 1 second
    GOOD: 2000,      // < 2 seconds
    FAIR: 3000,      // < 3 seconds
    SLOW: 5000       // >= 3 seconds
  }
} as const;

// Type for the configuration object
export type MatchingMayhemConfig = typeof MATCHING_MAYHEM_CONFIG;
