#!/usr/bin/env node

/**
 * JWT Generation Test
 * Tests the JWT generation endpoint for all supported game types
 */

import { jwtService } from '../services/jwtService.js';
import { logger } from '../utils/logger.js';

// Test data for different game types
const testGameTypes = ['finger-frenzy', 'bingo', 'matching-mayhem', 'numbers'];

async function testJWTGeneration() {
  console.log('🧪 Testing JWT Generation for All Game Types\n');

  for (const gameId of testGameTypes) {
    try {
      console.log(`Testing ${gameId}...`);
      
      // Create test user data
      const userData = {
        userId: `test-user-${Date.now()}`,
        username: `TestPlayer-${gameId}`,
        email: `test-${gameId}@example.com`,
        gameId,
        roomId: `test-room-${gameId}-${Date.now()}`,
        scoreSubmitId: `test-score-${Date.now()}`,
        authToken: `test-auth-${Date.now()}`
      };

      // Generate JWT token
      const token = jwtService.generateToken(userData);
      console.log(`✅ Token generated for ${gameId}`);
      console.log(`   Token length: ${token.length} characters`);

      // Validate the token
      const validatedUser = jwtService.validateToken(token);
      if (!validatedUser) {
        throw new Error('Token validation failed');
      }

      console.log(`✅ Token validated for ${gameId}`);
      console.log(`   User ID: ${validatedUser.userId}`);
      console.log(`   Game ID: ${validatedUser.gameId}`);
      console.log(`   Room ID: ${validatedUser.roomId}`);
      console.log(`   Score Submit ID: ${validatedUser.scoreSubmitId}`);

      // Verify all required fields are present
      const requiredFields = ['userId', 'gameId', 'roomId', 'scoreSubmitId', 'authToken'];
      for (const field of requiredFields) {
        if (!validatedUser[field as keyof typeof validatedUser]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      console.log(`✅ All required fields present for ${gameId}\n`);

    } catch (error) {
      console.error(`❌ Test failed for ${gameId}:`, error);
      process.exit(1);
    }
  }

  console.log('🎉 All JWT generation tests passed!');
}

// Test token expiration
async function testTokenExpiration() {
  console.log('\n🧪 Testing Token Expiration\n');

  try {
    const userData = {
      userId: 'test-user-expiration',
      username: 'TestPlayer',
      email: '<EMAIL>',
      gameId: 'finger-frenzy',
      roomId: 'test-room-expiration',
      scoreSubmitId: 'test-score-expiration',
      authToken: 'test-auth-expiration'
    };

    const token = jwtService.generateToken(userData);
    
    // Check if token is not expired (should be valid for 1 hour)
    const isExpired = jwtService.isTokenExpired(token);
    if (isExpired) {
      throw new Error('Newly generated token should not be expired');
    }

    console.log('✅ Token expiration check passed');

  } catch (error) {
    console.error('❌ Token expiration test failed:', error);
    process.exit(1);
  }
}

// Test invalid game types
async function testInvalidGameTypes() {
  console.log('\n🧪 Testing Invalid Game Types\n');

  const invalidGameTypes = ['invalid-game', 'not-a-game', ''];

  for (const gameId of invalidGameTypes) {
    try {
      const userData = {
        userId: 'test-user',
        username: 'TestPlayer',
        email: '<EMAIL>',
        gameId,
        roomId: 'test-room',
        scoreSubmitId: 'test-score',
        authToken: 'test-auth'
      };

      const token = jwtService.generateToken(userData);
      const validatedUser = jwtService.validateToken(token);

      // The token should still be generated and validated (server doesn't validate game types in JWT service)
      // Game type validation happens at the API endpoint level
      console.log(`ℹ️  Token generated for invalid game type "${gameId}" (validation happens at API level)`);

    } catch (error) {
      console.log(`ℹ️  Expected behavior for invalid game type "${gameId}":`, error.message);
    }
  }

  console.log('✅ Invalid game type tests completed');
}

// Run all tests
async function runAllTests() {
  try {
    await testJWTGeneration();
    await testTokenExpiration();
    await testInvalidGameTypes();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- JWT generation works for all supported game types');
    console.log('- Token validation works correctly');
    console.log('- Token expiration handling works');
    console.log('- All required fields are properly included');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}

export { testJWTGeneration, testTokenExpiration, testInvalidGameTypes };
