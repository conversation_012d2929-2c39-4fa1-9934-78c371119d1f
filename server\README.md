# TicTaps Games - Node.js Game Server

## Overview

The TicTaps Games server is a Node.js-based real-time game server that handles multiplayer game sessions, authentication, and score management for the TicTaps Games platform. It serves as the central hub for real-time communication between game clients and manages game state, rooms, and player interactions.

### Architecture Role

```
PWA -->|auth api| PY
PY -->|auth api| NODE_GAMES (THIS SERVER)
PWA -->|iFrame postMessage: auth token, submit score ID, room ID, opponent score, game ID| GameApp
GameApp -->|SOCKET| NODE_GAMES (THIS SERVER)
NODE_GAMES -->|auth api, submit score api| PY
NODE_GAMES -->|active rooms, scores, timers| REDIS
PY --> DB
```

The server acts as the **NODE_GAMES** component, providing:

- Real-time multiplayer game communication via Socket.IO
- Game room management and player matching
- Score validation and submission to Python backend
- Authentication token validation with Python backend
- Game state synchronization and timer management

## Technology Stack

- **Node.js** with **Express.js** - Web server framework
- **Socket.IO** - Real-time bidirectional communication
- **TypeScript** - Type safety and development experience
- **Redis** - Session and game state management (planned)
- **JWT** - Authentication token validation

## Supported Games

The server supports the following Phaser 3 games:

- **Finger Frenzy** - Fast-paced finger tapping game
- **Bingo** - Classic bingo with multiplayer features
- **Matching Mayhem** - Memory/matching card game
- **Number Sequence** - Pattern recognition and sequence game

## Project Structure

```
server/
├── src/
│   ├── index.ts                 # Main server entry point
│   ├── middleware/
│   │   ├── auth.ts             # Authentication middleware
│   │   └── validation.ts       # Request validation
│   ├── services/
│   │   ├── authService.ts      # Python backend auth integration
│   │   ├── gameService.ts      # Game logic and validation
│   │   ├── roomService.ts      # Room management
│   │   └── scoreService.ts     # Score submission and validation
│   ├── models/
│   │   ├── Room.ts             # Room data structure
│   │   ├── Player.ts           # Player data structure
│   │   └── GameState.ts        # Game state management
│   ├── controllers/
│   │   ├── gameController.ts   # Game-specific logic
│   │   └── roomController.ts   # Room management logic
│   ├── sockets/
│   │   ├── gameEvents.ts       # Game-related socket events
│   │   ├── roomEvents.ts       # Room-related socket events
│   │   └── playerEvents.ts     # Player connection/disconnection
│   ├── utils/
│   │   ├── logger.ts           # Logging utilities
│   │   ├── validation.ts       # Data validation helpers
│   │   └── constants.ts        # Game constants and config
│   └── types/
│       ├── game.ts             # Game-related type definitions
│       ├── room.ts             # Room-related type definitions
│       └── player.ts           # Player-related type definitions
├── package.json                # Dependencies and scripts
├── tsconfig.json              # TypeScript configuration
├── .env.example               # Environment variables template
└── README.md                  # This file
```

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm
- Redis (for production deployment)

### Installation

1. Navigate to the server directory:

   ```bash
   cd server
   ```

2. Install dependencies:

   ```bash
   pnpm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Development

Start the development server with hot reload:

```bash
pnpm run dev
```

The server will start on `http://localhost:3000` by default.

### Available Scripts

- `pnpm run dev` - Start development server with hot reload
- `pnpm run build` - Build TypeScript to JavaScript (planned)
- `pnpm run start` - Start production server (planned)
- `pnpm run test` - Run test suite (planned)

## API Endpoints

### REST API

- `GET /` - Health check endpoint
- `POST /auth/validate` - Validate authentication token (planned)
- `POST /scores/submit` - Submit game scores (planned)
- `GET /rooms/:id` - Get room information (planned)

### Socket.IO Events

#### Client → Server

- `join_room` - Join a game room
- `leave_room` - Leave a game room
- `game_action` - Send game-specific action
- `submit_score` - Submit game score
- `player_ready` - Mark player as ready

#### Server → Client

- `room_joined` - Confirmation of room join
- `room_left` - Confirmation of room leave
- `game_state_update` - Game state synchronization
- `opponent_action` - Opponent's game action
- `game_start` - Game session start
- `game_end` - Game session end
- `score_update` - Real-time score updates

## Configuration

### Environment Variables

```env
PORT=3000
NODE_ENV=development
PYTHON_BACKEND_URL=http://localhost:8000
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=http://localhost:5173
```
