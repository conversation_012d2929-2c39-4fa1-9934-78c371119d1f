import { GameState } from './game';

export interface NumberSequenceGameState extends GameState {
  currentSequence: number[];
  currentIndex: number;
  streak: number;
  currentLevel: number;
  isRoundActive: boolean;
  roundStartTime: number | null;
  roundsCompleted: number;
}

export interface SequenceSelectActionData {
  gameId: string;
  roomId: string;
  action: {
    type: 'sequence_select';
    data: {
      selectedNumber: number;
      reactionTime?: number;
      clickTime: number;
    }
  }
}

export interface NumberSequenceGameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime: number;
  };
  currentSequence: number[];
  currentLevel: number;
  message: string;
}

export interface RoundData {
  sequence: number[];
  level: number;
  timeLimit: number;
}
